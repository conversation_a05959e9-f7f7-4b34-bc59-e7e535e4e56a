uuid: 14483648-e47b-45ba-ac99-4ff249f8e367
langcode: en
status: true
dependencies:
  module:
    - system
    - user
id: G-QSJC00HC5M.670141f5e30d74.70227639
label: G-QSJC00HC5M
weight: 0
tag_container_ids:
  - GTM-NSGVB9MN
advanced_settings:
  consent_mode: true
  gtm:
    GTM-NSGVB9MN:
      data_layer: dataLayer
      include_classes: false
      allowlist_classes: ''
      blocklist_classes: ''
      include_environment: false
      environment_id: ''
      environment_token: ''
dimensions_metrics:
  -
    type: dimension
    name: ''
    value: ''
conditions:
  request_path:
    id: request_path
    negate: true
    pages: "/admin\r\n/admin/*\r\n/batch\r\n/node/add*\r\n/v1/auctions_data"
  user_role:
    id: user_role
    negate: false
    context_mapping:
      user: '@user.current_user_context:current_user'
    roles:
      anonymous: anonymous
      authenticated: authenticated
      privileged: privileged
      privileged_vip: privileged_vip
      private_auction_member: private_auction_member
events:
  commerce_add_payment_info: {  }
  commerce_begin_checkout: {  }
  custom: {  }
  login:
    method: CMS
  contact_form_submission: {  }
  auction_created: {  }
  price_calculation: {  }
  context_sign_up: {  }
