uuid: 96bdd751-5b9a-4546-a9f7-d6c8e4ff3161
langcode: en
status: true
dependencies:
  config:
    - field.storage.paragraph.field_content
    - paragraphs.paragraphs_type.small_auction_teasers
  module:
    - text
id: paragraph.small_auction_teasers.field_content
field_name: field_content
entity_type: paragraph
bundle: small_auction_teasers
label: Content
description: ''
required: true
translatable: false
default_value: {  }
default_value_callback: ''
settings:
  allowed_formats: {  }
field_type: text_long
