uuid: 1754e700-7290-485d-a404-4a052ef18ae2
langcode: en
status: true
dependencies:
  config:
    - core.entity_view_mode.node.teaser
    - field.field.node.simplenews_issue.body
    - field.field.node.simplenews_issue.field_regions_of_interest
    - field.field.node.simplenews_issue.field_scheduled
    - field.field.node.simplenews_issue.simplenews_issue
    - node.type.simplenews_issue
  module:
    - text
    - user
_core:
  default_config_hash: n2xAKuCwCLXGUcR4BjVa08OIbG6UDdyIQFjq_S91h50
id: node.simplenews_issue.teaser
targetEntityType: node
bundle: simplenews_issue
mode: teaser
content:
  body:
    type: text_trimmed
    label: hidden
    settings:
      trim_length: 600
    third_party_settings: {  }
    weight: 0
    region: content
hidden:
  entity_print_view_epub: true
  entity_print_view_pdf: true
  entity_print_view_word_docx: true
  field_regions_of_interest: true
  field_scheduled: true
  langcode: true
  links: true
  simplenews_issue: true
