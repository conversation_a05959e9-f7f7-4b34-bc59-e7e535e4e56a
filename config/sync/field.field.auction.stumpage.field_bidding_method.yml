uuid: 4e3df050-5797-45f9-948d-e20e6d55daea
langcode: en
status: true
dependencies:
  config:
    - auctions.auction_type.stumpage
    - field.storage.auction.field_bidding_method
  module:
    - options
id: auction.stumpage.field_bidding_method
field_name: field_bidding_method
entity_type: auction
bundle: stumpage
label: 'Bidding method'
description: 'Specify whether the bidding is for a unit of measurement from the auction object (eg Eur/ha, Eur/cubic meter) or the entire bidding object.'
required: true
translatable: false
default_value:
  -
    value: total_price
default_value_callback: ''
settings: {  }
field_type: list_string
