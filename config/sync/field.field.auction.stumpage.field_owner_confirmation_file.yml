uuid: 6c18bd95-1bf0-411a-a4a8-b5ab2c0d6088
langcode: en
status: true
dependencies:
  config:
    - auctions.auction_type.stumpage
    - field.storage.auction.field_owner_confirmation_file
  module:
    - file
id: auction.stumpage.field_owner_confirmation_file
field_name: field_owner_confirmation_file
entity_type: auction
bundle: stumpage
label: 'Purchase agreement confirmation'
description: 'Please provide purchase agreement confirming the purchase of the felling from the owner.'
required: false
translatable: false
default_value: {  }
default_value_callback: ''
settings:
  handler: 'default:file'
  handler_settings: {  }
  file_directory: 'pac/[date:custom:Y]-[date:custom:m]'
  file_extensions: 'doc docx pdf jpg jpeg edoc'
  max_filesize: ''
  description_field: false
field_type: file
