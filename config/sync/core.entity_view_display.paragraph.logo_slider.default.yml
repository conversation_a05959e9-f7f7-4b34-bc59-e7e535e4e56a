uuid: 3fb41b11-1507-4909-83ae-2e8a7aad5dd9
langcode: en
status: true
dependencies:
  config:
    - field.field.paragraph.logo_slider.field_blocks
    - field.field.paragraph.logo_slider.field_title
    - paragraphs.paragraphs_type.logo_slider
  module:
    - slick_paragraphs
id: paragraph.logo_slider.default
targetEntityType: paragraph
bundle: logo_slider
mode: default
content:
  field_blocks:
    type: slick_paragraphs_vanilla
    label: hidden
    settings:
      optionset: logo_grid
      view_mode: default
      cache: 0
      skin: classic
      lazy: ''
      style: ''
      use_theme_field: false
      overridables:
        arrows: arrows
        autoplay: autoplay
        draggable: draggable
        infinite: infinite
        variableWidth: variableWidth
        dots: '0'
        mouseWheel: '0'
        randomize: '0'
      override: false
      skin_arrows: ''
      skin_dots: ''
    third_party_settings: {  }
    weight: 1
    region: content
  field_title:
    type: string
    label: hidden
    settings:
      link_to_entity: false
    third_party_settings: {  }
    weight: 0
    region: content
hidden:
  entity_print_view_epub: true
  entity_print_view_pdf: true
  entity_print_view_word_docx: true
