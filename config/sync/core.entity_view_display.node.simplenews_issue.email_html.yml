uuid: 595d84b8-78d5-46b1-855d-93f466a736b9
langcode: en
status: true
dependencies:
  config:
    - core.entity_view_mode.node.email_html
    - field.field.node.simplenews_issue.body
    - field.field.node.simplenews_issue.field_regions_of_interest
    - field.field.node.simplenews_issue.field_scheduled
    - field.field.node.simplenews_issue.simplenews_issue
    - node.type.simplenews_issue
  module:
    - text
    - user
_core:
  default_config_hash: k7huXut68pdao0qGHWeETfwtQudI0SZ260TyVyGTOFg
id: node.simplenews_issue.email_html
targetEntityType: node
bundle: simplenews_issue
mode: email_html
content:
  body:
    type: text_default
    label: hidden
    settings: {  }
    third_party_settings: {  }
    weight: 0
    region: content
hidden:
  entity_print_view_epub: true
  entity_print_view_pdf: true
  entity_print_view_word_docx: true
  field_regions_of_interest: true
  field_scheduled: true
  langcode: true
  links: true
  simplenews_issue: true
