uuid: 1ef83138-ea95-4690-b890-fc8092bac34a
langcode: en
status: true
dependencies:
  config:
    - commerce_order.commerce_order_item_type.default
  module:
    - commerce_price
  enforced:
    module:
      - commerce_product
_core:
  default_config_hash: a-MK2hTebnM3YwCg08ZKg7ItNkBUOsu3am_y0HIjTw8
id: commerce_order_item.default.default
targetEntityType: commerce_order_item
bundle: default
mode: default
content:
  created:
    type: timestamp
    label: hidden
    settings:
      date_format: medium
      custom_date_format: ''
      timezone: ''
      tooltip:
        date_format: ''
        custom_date_format: ''
      time_diff:
        enabled: false
        future_format: '@interval hence'
        past_format: '@interval ago'
        granularity: 2
        refresh: 60
    third_party_settings: {  }
    weight: 0
    region: content
  purchased_entity:
    type: entity_reference_entity_view
    label: above
    settings:
      view_mode: default
      link: false
    third_party_settings: {  }
    weight: 0
    region: content
  quantity:
    type: number_decimal
    label: above
    settings:
      thousand_separator: ''
      decimal_separator: .
      scale: 2
      prefix_suffix: true
    third_party_settings: {  }
    weight: 1
    region: content
  total_price:
    type: commerce_price_default
    label: above
    settings:
      strip_trailing_zeroes: false
      currency_display: symbol
    third_party_settings: {  }
    weight: 3
    region: content
  unit_price:
    type: commerce_price_default
    label: above
    settings:
      strip_trailing_zeroes: false
      currency_display: symbol
    third_party_settings: {  }
    weight: 2
    region: content
hidden:
  adjustments: true
  entity_print_view_epub: true
  entity_print_view_pdf: true
  entity_print_view_word_docx: true
