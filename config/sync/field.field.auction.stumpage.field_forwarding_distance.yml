uuid: 8db47bd1-6032-4336-837c-877618e05a36
langcode: en
status: true
dependencies:
  config:
    - auctions.auction_type.stumpage
    - field.storage.auction.field_forwarding_distance
  module:
    - require_on_publish
third_party_settings:
  require_on_publish:
    require_on_publish: true
id: auction.stumpage.field_forwarding_distance
field_name: field_forwarding_distance
entity_type: auction
bundle: stumpage
label: 'Forwarding distance'
description: 'The average delivery distance is the distance from the warehouse at the road to the centers of the felling areas, measured along the delivery road in meters.'
required: false
translatable: false
default_value: {  }
default_value_callback: ''
settings:
  min: 0
  max: 10000
  prefix: ''
  suffix: m
field_type: integer
