uuid: 90b3bf2f-b6d5-4eb4-a9e9-965fd6ac2082
langcode: en
status: true
dependencies:
  module:
    - user
id: user.register_pending_approval_admin
configuration:
  email_subject:
    value: 'Account details for [user:display-name] at [site:name] (pending admin approval)'
  email_body:
    content:
      value: |-
        <p>[user:display-name] has applied for an account at <a href="[site:url]">[site:name]</a>.
        You may now use this link to <a href="[user:edit-url]">approve the request</a>.</p>
      format: email_html
  email_to:
    addresses:
      -
        value: '<site>'
        display: ''
