uuid: 7335903c-8b3b-42db-9134-361d5a583569
langcode: en
status: true
dependencies:
  config:
    - auctions.auction_type.stumpage
    - field.storage.node.field_auction_reference
    - node.type.auction_offers
id: node.auction_offers.field_auction_reference
field_name: field_auction_reference
entity_type: node
bundle: auction_offers
label: Auction
description: ''
required: false
translatable: false
default_value: {  }
default_value_callback: ''
settings:
  handler: 'default:auction'
  handler_settings:
    target_bundles:
      stumpage: stumpage
    sort:
      field: _none
      direction: ASC
    auto_create: false
    auto_create_bundle: ''
field_type: entity_reference
