uuid: 04c92cb3-22ac-4033-802c-f671beed47ba
langcode: en
status: true
dependencies:
  config:
    - commerce_order.commerce_order_type.default
    - field.field.commerce_order.default.field_auction
    - field.field.commerce_order.default.field_comment
  module:
    - commerce_order
    - commerce_price
    - state_machine
    - user
_core:
  default_config_hash: evnV-nUZotGAM4kgVWKzCtGRJQMstQX5RxlGG7kx3fg
id: commerce_order.default.default
targetEntityType: commerce_order
bundle: default
mode: default
content:
  balance:
    type: commerce_price_default
    label: inline
    settings:
      strip_trailing_zeroes: false
      currency_display: symbol
    third_party_settings: {  }
    weight: 9
    region: content
  changed:
    type: timestamp
    label: inline
    settings:
      date_format: short
      custom_date_format: ''
      timezone: ''
      tooltip:
        date_format: ''
        custom_date_format: ''
      time_diff:
        enabled: false
        future_format: '@interval hence'
        past_format: '@interval ago'
        granularity: 2
        refresh: 60
    third_party_settings: {  }
    weight: 4
    region: content
  completed:
    type: timestamp
    label: inline
    settings:
      date_format: short
      custom_date_format: ''
      timezone: ''
      tooltip:
        date_format: ''
        custom_date_format: ''
      time_diff:
        enabled: false
        future_format: '@interval hence'
        past_format: '@interval ago'
        granularity: 2
        refresh: 60
    third_party_settings: {  }
    weight: 2
    region: content
  field_comment:
    type: basic_string
    label: above
    settings: {  }
    third_party_settings: {  }
    weight: 10
    region: content
  ip_address:
    type: string
    label: inline
    settings:
      link_to_entity: false
    third_party_settings: {  }
    weight: 7
    region: content
  mail:
    type: basic_string
    label: inline
    settings: {  }
    third_party_settings: {  }
    weight: 6
    region: content
  order_items:
    type: commerce_order_item_table
    label: hidden
    settings: {  }
    third_party_settings: {  }
    weight: 0
    region: content
  placed:
    type: timestamp
    label: inline
    settings:
      date_format: short
      custom_date_format: ''
      timezone: ''
      tooltip:
        date_format: ''
        custom_date_format: ''
      time_diff:
        enabled: false
        future_format: '@interval hence'
        past_format: '@interval ago'
        granularity: 2
        refresh: 60
    third_party_settings: {  }
    weight: 3
    region: content
  state:
    type: state_transition_form
    label: hidden
    settings:
      require_confirmation: false
      use_modal: false
    third_party_settings: {  }
    weight: 8
    region: content
  total_price:
    type: commerce_order_total_summary
    label: hidden
    settings: {  }
    third_party_settings: {  }
    weight: 1
    region: content
  uid:
    type: author
    label: inline
    settings: {  }
    third_party_settings: {  }
    weight: 5
    region: content
hidden:
  billing_profile: true
  entity_print_view_epub: true
  entity_print_view_pdf: true
  entity_print_view_word_docx: true
  field_auction: true
  order_number: true
  store_id: true
  total_paid: true
