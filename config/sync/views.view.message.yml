uuid: 068148dd-074a-4c51-b971-059388e95ba3
langcode: en
status: true
dependencies:
  config:
    - field.storage.message.field_type
  module:
    - better_exposed_filters
    - message
    - options
    - user
_core:
  default_config_hash: 9wYMknhvj6Xl2rA2SU8aeAU9DiPxMW6sQxRYyGwevr8
id: message
label: Message
module: views
description: ''
tag: ''
base_table: message_field_data
base_field: mid
display:
  default:
    id: default
    display_title: Master
    display_plugin: default
    position: 0
    display_options:
      title: Message
      fields:
        message_bulk_form_1:
          id: message_bulk_form_1
          table: message
          field: message_bulk_form
          relationship: none
          group_type: group
          admin_label: ''
          entity_type: message
          plugin_id: bulk_form
          label: 'Message operations bulk form'
          exclude: false
          alter:
            alter_text: false
            text: ''
            make_link: false
            path: ''
            absolute: false
            external: false
            replace_spaces: false
            path_case: none
            trim_whitespace: false
            alt: ''
            rel: ''
            link_class: ''
            prefix: ''
            suffix: ''
            target: ''
            nl2br: false
            max_length: 0
            word_boundary: true
            ellipsis: true
            more_link: false
            more_link_text: ''
            more_link_path: ''
            strip_tags: false
            trim: false
            preserve_tags: ''
            html: false
          element_type: ''
          element_class: ''
          element_label_type: ''
          element_label_class: ''
          element_label_colon: true
          element_wrapper_type: ''
          element_wrapper_class: ''
          element_default_classes: true
          empty: ''
          hide_empty: false
          empty_zero: false
          hide_alter_empty: true
          action_title: 'With selection'
          include_exclude: include
          selected_actions:
            - message_delete_action
        mid:
          id: mid
          table: message_field_data
          field: mid
          relationship: none
          group_type: group
          admin_label: ''
          entity_type: null
          entity_field: mid
          plugin_id: field
          label: 'Message ID'
          exclude: false
          alter:
            alter_text: false
            text: ''
            make_link: false
            path: ''
            absolute: false
            external: false
            replace_spaces: false
            path_case: none
            trim_whitespace: false
            alt: ''
            rel: ''
            link_class: ''
            prefix: ''
            suffix: ''
            target: ''
            nl2br: false
            max_length: 0
            word_boundary: true
            ellipsis: true
            more_link: false
            more_link_text: ''
            more_link_path: ''
            strip_tags: false
            trim: false
            preserve_tags: ''
            html: false
          element_type: ''
          element_class: ''
          element_label_type: ''
          element_label_class: ''
          element_label_colon: true
          element_wrapper_type: ''
          element_wrapper_class: ''
          element_default_classes: true
          empty: ''
          hide_empty: false
          empty_zero: false
          hide_alter_empty: true
          click_sort_column: value
          type: number_integer
          settings:
            thousand_separator: ''
            prefix_suffix: true
          group_column: value
          group_columns: {  }
          group_rows: true
          delta_limit: 0
          delta_offset: 0
          delta_reversed: false
          delta_first_last: false
          multi_type: separator
          separator: ', '
          field_api_classes: false
        template:
          id: template
          table: message_field_data
          field: template
          relationship: none
          group_type: group
          admin_label: ''
          entity_type: message
          entity_field: template
          plugin_id: field
          label: 'Message template'
          exclude: false
          alter:
            alter_text: false
            text: ''
            make_link: false
            path: ''
            absolute: false
            external: false
            replace_spaces: false
            path_case: none
            trim_whitespace: false
            alt: ''
            rel: ''
            link_class: ''
            prefix: ''
            suffix: ''
            target: ''
            nl2br: false
            max_length: 0
            word_boundary: true
            ellipsis: true
            more_link: false
            more_link_text: ''
            more_link_path: ''
            strip_tags: false
            trim: false
            preserve_tags: ''
            html: false
          element_type: ''
          element_class: ''
          element_label_type: ''
          element_label_class: ''
          element_label_colon: false
          element_wrapper_type: ''
          element_wrapper_class: ''
          element_default_classes: true
          empty: ''
          hide_empty: false
          empty_zero: false
          hide_alter_empty: true
          click_sort_column: target_id
          type: entity_reference_label
          settings:
            link: true
          group_column: target_id
          group_columns: {  }
          group_rows: true
          delta_limit: 0
          delta_offset: 0
          delta_reversed: false
          delta_first_last: false
          multi_type: separator
          separator: ', '
          field_api_classes: false
        get_text:
          id: get_text
          table: message
          field: get_text
          relationship: none
          group_type: group
          admin_label: ''
          entity_type: message
          plugin_id: get_text
          label: 'Message text'
          exclude: false
          alter:
            alter_text: false
            text: ''
            make_link: false
            path: ''
            absolute: false
            external: false
            replace_spaces: false
            path_case: none
            trim_whitespace: false
            alt: ''
            rel: ''
            link_class: ''
            prefix: ''
            suffix: ''
            target: ''
            nl2br: false
            max_length: 500
            word_boundary: true
            ellipsis: false
            more_link: false
            more_link_text: ''
            more_link_path: ''
            strip_tags: false
            trim: false
            preserve_tags: ''
            html: true
          element_type: ''
          element_class: ''
          element_label_type: ''
          element_label_class: ''
          element_label_colon: true
          element_wrapper_type: ''
          element_wrapper_class: ''
          element_default_classes: true
          empty: ''
          hide_empty: false
          empty_zero: false
          hide_alter_empty: true
        field_type:
          id: field_type
          table: message__field_type
          field: field_type
          relationship: none
          group_type: group
          admin_label: ''
          plugin_id: field
          label: Type
          exclude: false
          alter:
            alter_text: false
            text: ''
            make_link: false
            path: ''
            absolute: false
            external: false
            replace_spaces: false
            path_case: none
            trim_whitespace: false
            alt: ''
            rel: ''
            link_class: ''
            prefix: ''
            suffix: ''
            target: ''
            nl2br: false
            max_length: 0
            word_boundary: true
            ellipsis: true
            more_link: false
            more_link_text: ''
            more_link_path: ''
            strip_tags: false
            trim: false
            preserve_tags: ''
            html: false
          element_type: ''
          element_class: ''
          element_label_type: ''
          element_label_class: ''
          element_label_colon: true
          element_wrapper_type: ''
          element_wrapper_class: ''
          element_default_classes: true
          empty: ''
          hide_empty: false
          empty_zero: false
          hide_alter_empty: true
          click_sort_column: value
          type: list_default
          settings: {  }
          group_column: value
          group_columns: {  }
          group_rows: true
          delta_limit: 0
          delta_offset: 0
          delta_reversed: false
          delta_first_last: false
          multi_type: separator
          separator: ', '
          field_api_classes: false
        uid:
          id: uid
          table: message_field_data
          field: uid
          relationship: none
          group_type: group
          admin_label: ''
          entity_type: message
          entity_field: uid
          plugin_id: field
          label: User
          exclude: false
          alter:
            alter_text: false
            text: ''
            make_link: false
            path: ''
            absolute: false
            external: false
            replace_spaces: false
            path_case: none
            trim_whitespace: false
            alt: ''
            rel: ''
            link_class: ''
            prefix: ''
            suffix: ''
            target: ''
            nl2br: false
            max_length: 0
            word_boundary: true
            ellipsis: true
            more_link: false
            more_link_text: ''
            more_link_path: ''
            strip_tags: false
            trim: false
            preserve_tags: ''
            html: false
          element_type: ''
          element_class: ''
          element_label_type: ''
          element_label_class: ''
          element_label_colon: false
          element_wrapper_type: ''
          element_wrapper_class: ''
          element_default_classes: true
          empty: ''
          hide_empty: false
          empty_zero: false
          hide_alter_empty: true
          click_sort_column: target_id
          type: entity_reference_label
          settings:
            link: true
          group_column: target_id
          group_columns: {  }
          group_rows: true
          delta_limit: 0
          delta_offset: 0
          delta_reversed: false
          delta_first_last: false
          multi_type: separator
          separator: ', '
          field_api_classes: false
        created:
          id: created
          table: message_field_data
          field: created
          relationship: none
          group_type: group
          admin_label: ''
          entity_type: message
          entity_field: created
          plugin_id: date
          label: Date
          exclude: false
          alter:
            alter_text: false
            text: ''
            make_link: false
            path: ''
            absolute: false
            external: false
            replace_spaces: false
            path_case: none
            trim_whitespace: false
            alt: ''
            rel: ''
            link_class: ''
            prefix: ''
            suffix: ''
            target: ''
            nl2br: false
            max_length: 0
            word_boundary: true
            ellipsis: true
            more_link: false
            more_link_text: ''
            more_link_path: ''
            strip_tags: false
            trim: false
            preserve_tags: ''
            html: false
          element_type: ''
          element_class: ''
          element_label_type: ''
          element_label_class: ''
          element_label_colon: false
          element_wrapper_type: ''
          element_wrapper_class: ''
          element_default_classes: true
          empty: ''
          hide_empty: false
          empty_zero: false
          hide_alter_empty: true
          date_format: fallback
          custom_date_format: ''
          timezone: ''
          click_sort_column: value
          type: timestamp
          settings:
            time_diff:
              enabled: 0
              future_format: '@interval hence'
              past_format: '@interval ago'
              granularity: '2'
              refresh: '60'
              description: ''
            date_format: short
            custom_date_format: ''
            timezone: ''
            tooltip:
              date_format: long
              custom_date_format: ''
          group_column: value
          group_columns: {  }
          group_rows: true
          delta_limit: 0
          delta_offset: 0
          delta_reversed: false
          delta_first_last: false
          multi_type: separator
          separator: ', '
          field_api_classes: 0
      pager:
        type: full
        options:
          offset: 0
          pagination_heading_level: h4
          items_per_page: 25
          total_pages: null
          id: 0
          tags:
            next: 'next ›'
            previous: '‹ previous'
            first: '« first'
            last: 'last »'
          expose:
            items_per_page: false
            items_per_page_label: 'Items per page'
            items_per_page_options: '5, 10, 25, 50'
            items_per_page_options_all: false
            items_per_page_options_all_label: '- All -'
            offset: false
            offset_label: Offset
          quantity: 9
      exposed_form:
        type: bef
        options:
          submit_button: Apply
          reset_button: true
          reset_button_label: Reset
          exposed_sorts_label: 'Sort by'
          expose_sort_order: true
          sort_asc_label: Asc
          sort_desc_label: Desc
          text_input_required: 'Select any filter and click on Apply to see results'
          text_input_required_format: full
          bef:
            general:
              autosubmit: false
              autosubmit_exclude_textfield: false
              autosubmit_textfield_delay: 500
              autosubmit_textfield_minimum_length: 3
              autosubmit_hide: false
              input_required: false
              allow_secondary: false
              secondary_label: 'Advanced options'
              secondary_open: false
              reset_button_always_show: false
            filter:
              template:
                plugin_id: default
                advanced:
                  sort_options: false
                  rewrite:
                    filter_rewrite_values: ''
                    filter_rewrite_values_key: false
                  collapsible: false
                  collapsible_disable_automatic_open: false
                  is_secondary: false
                  hide_label: false
              field_type_value:
                plugin_id: default
                advanced:
                  sort_options: false
                  rewrite:
                    filter_rewrite_values: ''
                    filter_rewrite_values_key: false
                  collapsible: false
                  collapsible_disable_automatic_open: false
                  is_secondary: false
                  hide_label: false
              uid:
                plugin_id: default
                advanced:
                  placeholder_text: ''
                  rewrite:
                    filter_rewrite_values: ''
                    filter_rewrite_values_key: false
                  collapsible: false
                  collapsible_disable_automatic_open: false
                  is_secondary: false
                  hide_label: false
              created:
                plugin_id: bef_datepicker
                advanced:
                  collapsible: false
                  collapsible_disable_automatic_open: false
                  is_secondary: false
                  hide_label: false
              created_1:
                plugin_id: bef_datepicker
                advanced:
                  collapsible: false
                  collapsible_disable_automatic_open: false
                  is_secondary: false
                  hide_label: false
      access:
        type: perm
        options:
          perm: 'overview messages'
      cache:
        type: none
        options: {  }
      empty:
        area:
          id: area
          table: views
          field: area
          relationship: none
          group_type: group
          admin_label: ''
          plugin_id: text
          empty: true
          content:
            value: 'No messages created yet'
            format: full
          tokenize: false
      sorts: {  }
      arguments: {  }
      filters:
        uid:
          id: uid
          table: message_field_data
          field: uid
          relationship: none
          group_type: group
          admin_label: ''
          entity_type: message
          entity_field: uid
          plugin_id: user_name
          operator: in
          value: {  }
          group: 1
          exposed: true
          expose:
            operator_id: uid_op
            label: User
            description: ''
            use_operator: false
            operator: uid_op
            operator_limit_selection: false
            operator_list: {  }
            identifier: uid
            required: false
            remember: false
            multiple: false
            remember_roles:
              authenticated: authenticated
              anonymous: '0'
              editor: '0'
              administrator: '0'
              privileged: '0'
              privileged_vip: '0'
              private_auction_member: '0'
            reduce: false
          is_grouped: false
          group_info:
            label: ''
            description: ''
            identifier: ''
            optional: true
            widget: select
            multiple: false
            remember: false
            default_group: All
            default_group_multiple: {  }
            group_items: {  }
        template:
          id: template
          table: message_field_data
          field: template
          relationship: none
          group_type: group
          admin_label: ''
          entity_type: message
          entity_field: template
          plugin_id: bundle
          operator: in
          value: {  }
          group: 1
          exposed: true
          expose:
            operator_id: template_op
            label: Template
            description: ''
            use_operator: false
            operator: template_op
            identifier: template
            required: false
            remember: false
            multiple: false
            remember_roles:
              authenticated: authenticated
              anonymous: '0'
              administrator: '0'
            reduce: false
          is_grouped: false
          group_info:
            label: ''
            description: ''
            identifier: ''
            optional: true
            widget: select
            multiple: false
            remember: false
            default_group: All
            default_group_multiple: {  }
            group_items: {  }
        field_type_value:
          id: field_type_value
          table: message__field_type
          field: field_type_value
          relationship: none
          group_type: group
          admin_label: ''
          plugin_id: list_field
          operator: or
          value: {  }
          group: 1
          exposed: true
          expose:
            operator_id: field_type_value_op
            label: Type
            description: ''
            use_operator: false
            operator: field_type_value_op
            operator_limit_selection: false
            operator_list: {  }
            identifier: field_type_value
            required: false
            remember: false
            multiple: false
            remember_roles:
              authenticated: authenticated
              anonymous: '0'
              editor: '0'
              administrator: '0'
              privileged: '0'
              privileged_vip: '0'
              private_auction_member: '0'
            reduce: false
          is_grouped: false
          group_info:
            label: ''
            description: ''
            identifier: ''
            optional: true
            widget: select
            multiple: false
            remember: false
            default_group: All
            default_group_multiple: {  }
            group_items: {  }
          reduce_duplicates: false
        created:
          id: created
          table: message_field_data
          field: created
          relationship: none
          group_type: group
          admin_label: ''
          entity_type: message
          entity_field: created
          plugin_id: date
          operator: '>='
          value:
            min: ''
            max: ''
            value: ''
            type: date
          group: 1
          exposed: true
          expose:
            operator_id: created_op
            label: From
            description: ''
            use_operator: false
            operator: created_op
            operator_limit_selection: false
            operator_list: {  }
            identifier: from
            required: false
            remember: false
            multiple: false
            remember_roles:
              authenticated: authenticated
              anonymous: '0'
              editor: '0'
              administrator: '0'
              privileged: '0'
              privileged_vip: '0'
              private_auction_member: '0'
            min_placeholder: ''
            max_placeholder: ''
            placeholder: ''
          is_grouped: false
          group_info:
            label: ''
            description: ''
            identifier: ''
            optional: true
            widget: select
            multiple: false
            remember: false
            default_group: All
            default_group_multiple: {  }
            group_items: {  }
        created_1:
          id: created_1
          table: message_field_data
          field: created
          relationship: none
          group_type: group
          admin_label: ''
          entity_type: message
          entity_field: created
          plugin_id: date
          operator: '<='
          value:
            min: ''
            max: ''
            value: ''
            type: date
          group: 1
          exposed: true
          expose:
            operator_id: created_1_op
            label: To
            description: ''
            use_operator: false
            operator: created_1_op
            operator_limit_selection: false
            operator_list: {  }
            identifier: to
            required: false
            remember: false
            multiple: false
            remember_roles:
              authenticated: authenticated
              anonymous: '0'
              editor: '0'
              administrator: '0'
              privileged: '0'
              privileged_vip: '0'
              private_auction_member: '0'
            min_placeholder: ''
            max_placeholder: ''
            placeholder: ''
          is_grouped: false
          group_info:
            label: ''
            description: ''
            identifier: ''
            optional: true
            widget: select
            multiple: false
            remember: false
            default_group: All
            default_group_multiple: {  }
            group_items: {  }
        uid_raw:
          id: uid_raw
          table: users_field_data
          field: uid_raw
          relationship: uid
          group_type: group
          admin_label: ''
          entity_type: user
          plugin_id: numeric
          operator: '='
          value:
            min: ''
            max: ''
            value: ''
          group: 1
          exposed: true
          expose:
            operator_id: uid_raw_op
            label: 'The user ID'
            description: null
            use_operator: false
            operator: uid_raw_op
            operator_limit_selection: false
            operator_list: {  }
            identifier: uid_raw
            required: false
            remember: false
            multiple: false
            remember_roles:
              authenticated: authenticated
            min_placeholder: null
            max_placeholder: null
            placeholder: null
          is_grouped: true
          group_info:
            label: 'User type'
            description: ''
            identifier: uid_raw
            optional: true
            widget: select
            multiple: false
            remember: false
            default_group: '2'
            default_group_multiple: {  }
            group_items:
              1:
                title: Anonymous
                operator: '='
                value:
                  min: ''
                  max: ''
                  value: '0'
              2:
                title: Registered
                operator: '!='
                value:
                  min: ''
                  max: ''
                  value: '0'
      filter_groups:
        operator: AND
        groups:
          1: AND
      style:
        type: table
        options:
          grouping: {  }
          row_class: ''
          default_row_class: true
          columns:
            message_bulk_form_1: message_bulk_form_1
            mid: mid
            template: template
            get_text: get_text
            uid: uid
            created: created
          default: mid
          info:
            message_bulk_form_1:
              align: ''
              separator: ''
              empty_column: false
              responsive: ''
            mid:
              sortable: true
              default_sort_order: desc
              align: ''
              separator: ''
              empty_column: false
              responsive: ''
            template:
              sortable: true
              default_sort_order: asc
              align: ''
              separator: ''
              empty_column: false
              responsive: priority-medium
            get_text:
              sortable: false
              default_sort_order: desc
              align: ''
              separator: ''
              empty_column: false
              responsive: ''
            uid:
              sortable: true
              default_sort_order: asc
              align: ''
              separator: ''
              empty_column: false
              responsive: priority-medium
            created:
              sortable: true
              default_sort_order: desc
              align: ''
              separator: ''
              empty_column: false
              responsive: priority-low
          override: true
          sticky: false
          summary: ''
          empty_table: false
          caption: ''
          description: ''
      row:
        type: fields
      query:
        type: views_query
        options:
          query_comment: ''
          disable_sql_rewrite: false
          distinct: false
          replica: false
          query_tags: {  }
      relationships:
        uid:
          id: uid
          table: message_field_data
          field: uid
          relationship: none
          group_type: group
          admin_label: User
          entity_type: message
          entity_field: uid
          plugin_id: standard
          required: true
      header: {  }
      footer: {  }
      display_extenders: {  }
    cache_metadata:
      max-age: 0
      contexts:
        - 'languages:language_content'
        - 'languages:language_interface'
        - url
        - url.query_args
        - user.permissions
      tags:
        - 'config:field.storage.message.field_type'
      cacheable: false
  page_1:
    id: page_1
    display_title: Page
    display_plugin: page
    position: 1
    display_options:
      display_extenders:
        metatag_display_extender:
          metatags: {  }
          tokenize: false
        ajax_history: {  }
      path: admin/content/message
      menu:
        type: tab
        title: Message
        description: ''
        weight: 0
        menu_name: admin
        parent: system.admin_content
        context: '0'
      tab_options:
        type: none
        title: ''
        description: ''
        weight: 0
    cache_metadata:
      max-age: 0
      contexts:
        - 'languages:language_content'
        - 'languages:language_interface'
        - url
        - url.query_args
        - user.permissions
      tags:
        - 'config:field.storage.message.field_type'
      cacheable: false
