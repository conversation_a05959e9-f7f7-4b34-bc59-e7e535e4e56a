uuid: c8130bc7-2619-4c06-9cef-edf2e6b1de5a
langcode: en
status: true
dependencies:
  config:
    - field.storage.paragraph.field_specie
    - paragraphs.paragraphs_type.species
  module:
    - options
id: paragraph.species.field_specie
field_name: field_specie
entity_type: paragraph
bundle: species
label: Specie
description: ''
required: true
translatable: true
default_value:
  -
    value: 'NV dastots'
default_value_callback: ''
settings: {  }
field_type: list_string
