uuid: 19753aa2-a801-48d5-9ebb-e5630242f0c7
langcode: en
status: true
dependencies:
  config:
    - field.field.customer.customer.field_tags
    - field.field.customer.customer.field_tasks
  module:
    - customer_relationship
    - paragraphs
    - text
id: customer.customer.default
targetEntityType: customer
bundle: customer
mode: default
content:
  description:
    type: text_textarea
    weight: 1
    region: content
    settings:
      rows: 5
      placeholder: ''
    third_party_settings: {  }
  field_tags:
    type: entity_reference_autocomplete_tags
    weight: 2
    region: content
    settings:
      match_operator: CONTAINS
      match_limit: 10
      size: 60
      placeholder: ''
    third_party_settings: {  }
  field_tasks:
    type: paragraphs
    weight: 3
    region: content
    settings:
      title: Task
      title_plural: Tasks
      edit_mode: open
      closed_mode: preview
      autocollapse: none
      closed_mode_threshold: 0
      add_mode: button
      form_display_mode: default
      default_paragraph_type: _none
      features:
        add_above: '0'
        collapse_edit_all: '0'
        duplicate: '0'
    third_party_settings: {  }
  uid:
    type: entity_reference_autocomplete
    weight: 0
    region: content
    settings:
      match_operator: CONTAINS
      match_limit: 10
      size: 60
      placeholder: ''
    third_party_settings: {  }
hidden: {  }
