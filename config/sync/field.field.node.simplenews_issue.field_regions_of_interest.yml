uuid: 3210df9d-1716-4d9a-b101-5ec46e104255
langcode: en
status: true
dependencies:
  config:
    - field.storage.node.field_regions_of_interest
    - node.type.simplenews_issue
    - taxonomy.vocabulary.regions
id: node.simplenews_issue.field_regions_of_interest
field_name: field_regions_of_interest
entity_type: node
bundle: simplenews_issue
label: 'Regions of interest'
description: ''
required: false
translatable: true
default_value: {  }
default_value_callback: ''
settings:
  handler: 'default:taxonomy_term'
  handler_settings:
    target_bundles:
      regions: regions
    sort:
      field: name
      direction: asc
    auto_create: false
    auto_create_bundle: administrative_areas
field_type: entity_reference
