uuid: 28adac30-35c2-43d3-95d1-e0e778ef6398
langcode: en
status: true
dependencies:
  config:
    - field.field.paragraph.person.field_content
    - field.field.paragraph.person.field_image
    - image.style.medium
    - paragraphs.paragraphs_type.person
  module:
    - image
    - text
id: paragraph.person.default
targetEntityType: paragraph
bundle: person
mode: default
content:
  field_content:
    type: text_textarea
    weight: 2
    region: content
    settings:
      rows: 5
      placeholder: ''
    third_party_settings: {  }
  field_image:
    type: image_image
    weight: 1
    region: content
    settings:
      progress_indicator: throbber
      preview_image_style: medium
    third_party_settings: {  }
  status:
    type: boolean_checkbox
    weight: 3
    region: content
    settings:
      display_label: true
    third_party_settings: {  }
  translation:
    weight: 0
    region: content
    settings: {  }
    third_party_settings: {  }
hidden:
  created: true
