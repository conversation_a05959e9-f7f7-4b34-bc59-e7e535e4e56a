uuid: ba4fde15-a038-4981-a6ec-720428cb9f2b
langcode: en
status: true
dependencies:
  config:
    - field.field.paragraph.text_and_cta.field_content
    - field.field.paragraph.text_and_cta.field_link
    - paragraphs.paragraphs_type.text_and_cta
  module:
    - link
    - text
id: paragraph.text_and_cta.default
targetEntityType: paragraph
bundle: text_and_cta
mode: default
content:
  field_content:
    type: text_textarea
    weight: 1
    region: content
    settings:
      rows: 5
      placeholder: ''
    third_party_settings: {  }
  field_link:
    type: link_default
    weight: 2
    region: content
    settings:
      placeholder_url: ''
      placeholder_title: ''
    third_party_settings: {  }
  status:
    type: boolean_checkbox
    weight: 3
    region: content
    settings:
      display_label: true
    third_party_settings: {  }
  translation:
    weight: 0
    region: content
    settings: {  }
    third_party_settings: {  }
hidden:
  created: true
