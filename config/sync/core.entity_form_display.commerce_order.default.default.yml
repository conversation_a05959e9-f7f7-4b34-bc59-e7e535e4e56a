uuid: 30cb381f-bd15-43bd-8ea1-149717af8825
langcode: en
status: true
dependencies:
  config:
    - commerce_order.commerce_order_type.default
    - field.field.commerce_order.default.field_auction
    - field.field.commerce_order.default.field_comment
  module:
    - commerce_order
    - inline_entity_form
_core:
  default_config_hash: 7hXqpb7yxuHJYNjyPmN_vQll6jq_fl7N09Tj-PFcESU
id: commerce_order.default.default
targetEntityType: commerce_order
bundle: default
mode: default
content:
  adjustments:
    type: commerce_adjustment_default
    weight: 2
    region: content
    settings: {  }
    third_party_settings: {  }
  billing_profile:
    type: commerce_billing_profile
    weight: 0
    region: content
    settings: {  }
    third_party_settings: {  }
  cart:
    type: boolean_checkbox
    weight: 3
    region: content
    settings:
      display_label: true
    third_party_settings: {  }
  coupons:
    type: entity_reference_autocomplete
    weight: 5
    region: content
    settings:
      match_operator: CONTAINS
      match_limit: 10
      size: 60
      placeholder: ''
    third_party_settings: {  }
  field_comment:
    type: string_textarea
    weight: 6
    region: content
    settings:
      rows: 5
      placeholder: ''
    third_party_settings: {  }
  order_items:
    type: inline_entity_form_complex
    weight: 1
    region: content
    settings:
      form_mode: default
      override_labels: true
      label_singular: 'order item'
      label_plural: 'order items'
      allow_new: true
      allow_existing: false
      match_operator: CONTAINS
      allow_duplicate: false
      collapsible: false
      collapsed: false
      revision: false
      removed_reference: delete
    third_party_settings: {  }
hidden:
  created: true
  field_auction: true
  ip_address: true
  mail: true
  order_number: true
  state: true
  store_id: true
  uid: true
