uuid: 1cab97ee-3b2b-48b6-b687-4a9c7f318a1b
langcode: en
status: true
dependencies:
  config:
    - field.field.profile.customer.address
    - field.field.profile.customer.field_additional_email
    - field.field.profile.customer.field_bank_account
    - field.field.profile.customer.field_bank_name
    - field.field.profile.customer.field_legal_status
    - field.field.profile.customer.field_name
    - field.field.profile.customer.field_registration_number
    - field.field.profile.customer.tax_number
    - profile.type.customer
  module:
    - address
    - commerce_tax
  enforced:
    module:
      - commerce_order
_core:
  default_config_hash: 3LyHGK7R4HxqejbxONet1S9j9ekqLJuFpwT-MNJygvo
id: profile.customer.default
targetEntityType: profile
bundle: customer
mode: default
content:
  address:
    type: address_default
    weight: 4
    region: content
    settings:
      wrapper_type: details
    third_party_settings: {  }
  field_additional_email:
    type: email_default
    weight: 26
    region: content
    settings:
      placeholder: ''
      size: 60
    third_party_settings: {  }
  field_bank_account:
    type: string_textfield
    weight: 8
    region: content
    settings:
      size: 60
      placeholder: ''
    third_party_settings: {  }
  field_bank_name:
    type: string_textfield
    weight: 6
    region: content
    settings:
      size: 60
      placeholder: ''
    third_party_settings: {  }
  field_legal_status:
    type: options_buttons
    weight: 0
    region: content
    settings: {  }
    third_party_settings: {  }
  field_name:
    type: string_textfield
    weight: 1
    region: content
    settings:
      size: 60
      placeholder: ''
    third_party_settings: {  }
  field_registration_number:
    type: string_textfield
    weight: 2
    region: content
    settings:
      size: 60
      placeholder: ''
    third_party_settings: {  }
  tax_number:
    type: commerce_tax_number_default
    weight: 3
    region: content
    settings: {  }
    third_party_settings: {  }
hidden:
  is_default: true
