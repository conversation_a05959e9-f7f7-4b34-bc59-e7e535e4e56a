diff --git a/queue_mail.module b/queue_mail.module
index 5647ea7..768c002 100644
--- a/queue_mail.module
+++ b/queue_mail.module
@@ -24,6 +24,11 @@ function queue_mail_mail_alter(&$message) {
     return;
   }
 
+  // Flag sent from queue worker: mail has come from the queue, so don't defer.
+  if (!empty($message['params']['queue_mail_queued'])) {
+    return;
+  }
+
   $config = \Drupal::config('queue_mail.settings');
   $mail_keys = $config->get('queue_mail_keys');
 
diff --git a/src/Plugin/QueueWorker/SendMailQueueWorker.php b/src/Plugin/QueueWorker/SendMailQueueWorker.php
index a532902..d9b2fe0 100644
--- a/src/Plugin/QueueWorker/SendMailQueueWorker.php
+++ b/src/Plugin/QueueWorker/SendMailQueueWorker.php
@@ -12,6 +12,7 @@ use Drupal\Core\Plugin\ContainerFactoryPluginInterface;
 use Drupal\Core\Queue\DelayedRequeueException;
 use Drupal\Core\Queue\QueueWorkerBase;
 use Drupal\Core\StringTranslation\StringTranslationTrait;
+use Drupal\Core\StringTranslation\TranslationManager;
 use Drupal\Core\Theme\ThemeInitializationInterface;
 use Drupal\Core\Theme\ThemeManagerInterface;
 use Symfony\Component\DependencyInjection\ContainerAwareInterface;
@@ -157,38 +158,20 @@ class SendMailQueueWorker extends QueueWorkerBase implements ContainerFactoryPlu
       return $message;
     }
 
-    // Retrieve the responsible implementation for this message.
-    $system = $this->mailManager->getInstance([
-      'module' => $message['module'],
-      'key' => $message['key'],
-    ]);
-
-    // Set theme that was used to generate mail body.
-    $this->setMailTheme($message);
-
-    // Set mail's language as active.
-    $current_langcode = $this->setMailLanguage($message);
-
-    try {
-      // Format the message body.
-      $message = $system->format($message);
-    }
-    finally {
-      // Revert the active theme, this is done inside a finally block so it is
-      // executed even if an exception is thrown during sending a mail.
-      $this->setActiveTheme($message);
-
-      // Revert the active language.
-      $this->setActiveLanguage($message, $current_langcode);
-    }
-
-    // Ensure that subject is plain text. By default translated and
-    // formatted strings are prepared for the HTML context and email
-    // subjects are plain strings.
-    if ($message['subject']) {
-      $message['subject'] = PlainTextOutput::renderFromHtml($message['subject']);
-    }
-    $message['result'] = $system->mail($message);
+    // Set flag for the queue_mail_mail_alter hook not to intercept the message.
+    $message['params']['queue_mail_queued'] = TRUE;
+
+    $current = \Drupal::languageManager()->getCurrentLanguage()->getId();
+    $this->changeActiveLanguage($message['langcode']);
+    $message = $this->mailManager->mail(
+      $message['module'],
+      $message['key'],
+      $message['to'],
+      $message['langcode'],
+      $message['params'],
+      $message['from'] ?? NULL
+    );
+    $this->changeActiveLanguage($current);
 
     // Log errors.
     if (!$message['result']) {
@@ -207,65 +190,37 @@ class SendMailQueueWorker extends QueueWorkerBase implements ContainerFactoryPlu
   }
 
   /**
-   * Sets language from the message.
-   *
-   * @param array $message
-   *   Mail message.
-   *
-   * @return string
-   *   The negotiated language code.
-   */
-  protected function setMailLanguage(array $message) {
-    return $message['langcode'];
-  }
-
-  /**
-   * Restores back the negotiated language.
+   * Changes the active language for translations.
    *
-   * @param array $message
-   *   Mail message.
    * @param string $langcode
-   *   The negotiated language code.
-   */
-  protected function setActiveLanguage(array $message, $langcode) {
-  }
-
-  /**
-   * Set theme from the theme.
-   *
-   * @param array $message
-   *   Mail message.
+   *   The langcode.
    */
-  protected function setMailTheme(array $message) {
-    if ($this->messageHasAnotherTheme($message)) {
-      $theme = $this->themeInitialization->initTheme($message['theme']);
-      $this->themeManager->setActiveTheme($theme);
+  protected function changeActiveLanguage($langcode) {
+    if (!\Drupal::languageManager()->isMultilingual()) {
+      return;
     }
-  }
-
-  /**
-   * Restore back theme that is used by default.
-   *
-   * @param array $message
-   *   Mail message.
-   */
-  protected function setActiveTheme(array $message) {
-    if ($this->messageHasAnotherTheme($message)) {
-      $this->themeManager->setActiveTheme($this->activeTheme);
+    $language = \Drupal::languageManager()->getLanguage($langcode);
+    if (!$language) {
+      return;
+    }
+    // The language manager has no method for overriding the default
+    // language, like it does for config overrides. We have to change the
+    // default language service's current language.
+    // @see https://www.drupal.org/project/drupal/issues/3029010
+    \Drupal::service('language.default')->set($language);
+    \Drupal::languageManager()->setConfigOverrideLanguage($language);
+    \Drupal::languageManager()->reset();
+
+    // The default string_translation service, TranslationManager, has a
+    // setDefaultLangcode method. However, this method is not present on
+    // either of its interfaces. Therefore we check for the concrete class
+    // here so that any swapped service does not break the application.
+    // @see https://www.drupal.org/project/drupal/issues/3029003
+    $string_translation = $this->getStringTranslation();
+    if ($string_translation instanceof TranslationManager) {
+      $string_translation->setDefaultLangcode($language->getId());
+      $string_translation->reset();
     }
-  }
-
-  /**
-   * Checks if message has been generated using another theme.
-   *
-   * @param array $message
-   *   Mail message.
-   *
-   * @return bool
-   *   TRUE if message has theme that is not an active theme, FALSE otherwise.
-   */
-  protected function messageHasAnotherTheme(array $message) {
-    return !empty($message['theme']) && $message['theme'] != $this->activeTheme->getName();
   }
 
   /**
