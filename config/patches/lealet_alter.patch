diff --git a/leaflet.module b/leaflet.module
index c46c204..8e0c6dd 100644
--- a/leaflet.module
+++ b/leaflet.module
@@ -43,13 +43,13 @@ function leaflet_map_get_info($map = NULL) {
     else {
       $map_info = \Drupal::moduleHandler()->invokeAll('leaflet_map_info');
 
-      // Let other modules alter the map info.
-      \Drupal::moduleHandler()->alter('leaflet_map_info', $map_info);
-
       \Drupal::cache()->set('leaflet_map_info', $map_info);
     }
   }
 
+  // Let other modules alter the map info.
+  \Drupal::moduleHandler()->alter('leaflet_map_info', $map_info);
+
   if (empty($map)) {
     return $map_info;
   }
