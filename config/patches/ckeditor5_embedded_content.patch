diff --git a/src/Plugin/Filter/EmbeddedContent.php b/src/Plugin/Filter/EmbeddedContent.php
index 1664528..6789fcc 100644
--- a/src/Plugin/Filter/EmbeddedContent.php
+++ b/src/Plugin/Filter/EmbeddedContent.php
@@ -102,7 +102,9 @@ class EmbeddedContent extends FilterBase implements ContainerFactoryPluginInterf
         $new = Html::load($render);
         libxml_clear_errors();
         $xpath = new \DOMXPath($new);
-        $new_node = $document->importNode($xpath->query('//body')->item(0), TRUE);
+        foreach ($xpath->query('//body/*') as $child) {
+          $new_node = $document->importNode($child, TRUE);
+        }
         libxml_use_internal_errors(FALSE);
         $node->parentNode->replaceChild($new_node, $node);
     });
