diff --git a/src/DatetimePopup.php b/src/DatetimePopup.php
index a5a6101..cde9ded 100644
--- a/src/DatetimePopup.php
+++ b/src/DatetimePopup.php
@@ -2,7 +2,9 @@
 
 namespace Drupal\date_popup;
 
+use Drupal\Component\Datetime\DateTimePlus;
 use Drupal\Core\Form\FormStateInterface;
+use Drupal\datetime\Plugin\Field\FieldType\DateTimeItemInterface;
 use Drupal\datetime\Plugin\views\filter\Date;
 
 /**
@@ -20,4 +22,22 @@ class DatetimePopup extends Date {
     static::applyDatePopupToForm($form, $this->options);
   }
 
+  /**
+   * Override parent method, which deals with dates as integers.
+   */
+  protected function opSimple($field) {
+    $timezone = $this->getTimezone();
+    $origin_offset = $this->getOffset($this->value['value'], $timezone);
+
+    // Convert to ISO. UTC timezone is used since dates are stored in UTC.
+    $value = new DateTimePlus($this->value['value'], new \DateTimeZone($timezone));
+    $add_day = $this->options['expose']['identifier'] == 'to' ? 24 * 60 * 60 : 0;
+
+    $value = $this->query->getDateFormat($this->query->getDateField("'" . $this->dateFormatter->format($value->getTimestamp() + $add_day + $origin_offset, 'custom', DateTimeItemInterface::DATETIME_STORAGE_FORMAT, DateTimeItemInterface::STORAGE_TIMEZONE) . "'", TRUE, $this->calculateOffset), $this->dateFormat, TRUE);
+
+    // This is safe because we are manually scrubbing the value.
+    $field = $this->query->getDateFormat($this->query->getDateField($field, TRUE, $this->calculateOffset), $this->dateFormat, TRUE);
+    $this->query->addWhereExpression($this->options['group'], "$field $this->operator $value");
+  }
+
 }
