diff --git a/src/EventSubscriber/InvoiceConfirmationSubscriber.php b/src/EventSubscriber/InvoiceConfirmationSubscriber.php
index 1941f9e..1937cee 100644
--- a/src/EventSubscriber/InvoiceConfirmationSubscriber.php
+++ b/src/EventSubscriber/InvoiceConfirmationSubscriber.php
@@ -53,7 +53,6 @@ class InvoiceConfirmationSubscriber implements EventSubscriberInterface, Destruc
   public static function getSubscribedEvents() {
     return [
       'commerce_invoice.confirm.post_transition' => ['sendInvoiceConfirmation', -100],
-      'commerce_invoice.pay.post_transition' => ['sendInvoiceConfirmation', -100],
     ];
   }
 
