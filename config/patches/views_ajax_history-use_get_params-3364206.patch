diff --git a/js/views_ajax_history.js b/js/views_ajax_history.js
index 5826bf4..11f0bbd 100644
--- a/js/views_ajax_history.js
+++ b/js/views_ajax_history.js
@@ -286,12 +286,19 @@
       var url = original.path + '?' + new URLSearchParams(new FormData(element.get(0))).toString();
       var currentQuery = parseQueryString(window.location.href);
 
+      // Prepare ajax url
+      var ajaxUrl = options.url.split('?')[0];
+      var ajaxQuery = parseQueryString(options.url);
+
       // Remove the page number from the query string, as a new filter has been
       // applied and should return new results.
       if ($.inArray("page", Object.keys(currentQuery)) !== -1) {
         delete currentQuery.page;
+        delete ajaxQuery.page;
       }
 
+      let reset = false;
+
       // Copy selected values in history state.
       $.each(form_values, function () {
         // Field name ending with [] is a multi value field.
@@ -305,12 +312,23 @@
         else {
           options.data[this.name] = this.value;
         }
+
+        if (this.name === 'reset') {
+          reset = true;
+        }
+
+        // Removes reset once more filters are selected after reset
+        // has been selected.
+        if (currentQuery['reset']) {
+          delete currentQuery['reset'];
+        }
       });
       // Remove exposed data from the current query to leave behind any
       // non exposed form related query vars.
       element.find('[name]').each(function () {
         if (currentQuery[this.name]) {
           delete currentQuery[this.name];
+          delete ajaxQuery[this.name];
         }
       });
 
@@ -324,6 +342,9 @@
           else if (options.data[this.name]) {
             delete options.data[this.name];
           }
+          if (ajaxQuery[this.name]) {
+            delete ajaxQuery[this.name];
+          }
         }
       });
 
@@ -332,11 +353,14 @@
         if ($(value).val().length === 0) {
           delete options.data[this.name];
           delete currentQuery[this.name];
+          delete ajaxQuery[this.name];
         }
       });
 
       url += (/\?/.test(url) ? '&' : '?') + $.param(currentQuery);
-      addState(options, url);
+      // Update options with updated ajax url.
+      options.url = ajaxUrl;
+      addState(options, reset ? original.path : url);
     }
     // Call the original Drupal method with the right context.
     Drupal.ViewsAjaxHistory.beforeSubmit.apply(this, arguments);
