diff --git a/src/Entity/Subscriber.php b/src/Entity/Subscriber.php
index 7eaf8f5..01ea35e 100644
--- a/src/Entity/Subscriber.php
+++ b/src/Entity/Subscriber.php
@@ -110,7 +110,9 @@ class Subscriber extends ContentEntityBase implements SubscriberInterface {
    * {@inheritdoc}
    */
   public function getUser() {
-    $uid = $this->getUserId();
+    if ($uid = $this->getUserId()) {
+      $this->entityTypeManager()->getStorage('user')->resetCache([$uid]);
+    }
     if ($uid && ($user = User::load($uid))) {
       return $user;
     }
