diff --git a/src/Plugin/Field/FieldWidget/OptionsShsWidget.php b/src/Plugin/Field/FieldWidget/OptionsShsWidget.php
index fd91719..1cbedc7 100644
--- a/src/Plugin/Field/FieldWidget/OptionsShsWidget.php
+++ b/src/Plugin/Field/FieldWidget/OptionsShsWidget.php
@@ -345,7 +345,7 @@ class OptionsShsWidget extends OptionsSelectWidget implements ContainerFactoryPl
       if (!$element['#required']) {
         return;
       }
-      elseif (count($element['#options']) > 1) {
+      elseif (count((array) $element['#options']) > 1) {
         $form_state->setError($element, t('You need to select a term from the deepest level in field @name.', ['@name' => $element['#title']]));
         return;
       }
