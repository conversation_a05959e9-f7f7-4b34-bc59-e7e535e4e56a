diff --git a/modules/language/src/Plugin/LanguageNegotiation/LanguageNegotiationBrowser.php b/modules/language/src/Plugin/LanguageNegotiation/LanguageNegotiationBrowser.php
--- a/modules/language/src/Plugin/LanguageNegotiation/LanguageNegotiationSelected.php
+++ b/modules/language/src/Plugin/LanguageNegotiation/LanguageNegotiationSelected.php
@@ -29,6 +29,10 @@
   public function getLangcode(Request $request = NULL) {
     $langcode = NULL;

+    if (PHP_SAPI === 'cli') {
+      return NULL;
+    }
+
     if ($this->languageManager) {
        $langcode = $this->config->get('language.negotiation')->get('selected_langcode');
     }
