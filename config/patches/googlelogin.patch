diff --git a/googlelogin.module b/googlelogin.module
index a60b580..a0ff46f 100644
--- a/googlelogin.module
+++ b/googlelogin.module
@@ -67,8 +67,9 @@ function googlelogin_login_button_code(&$form, $client_id = NULL) {
     $destination = \Drupal::request()->getRequestUri();
   }
   $tempstore->set('state_destination', $destination);
+  setcookie('google_login_destination', $destination, time() + (86400 * 30), "/");
   $hash = md5(rand());
-  $authUrl = str_replace('http:', 'https:', Url::fromRoute('googlelogin.callback', ['destination' => $destination])->setAbsolute(TRUE)->toString());
+  $authUrl = str_replace('http:', 'https:', Url::fromRoute('googlelogin.callback')->setAbsolute(TRUE)->toString());
 
   $form['#attached']['library'][] = 'googlelogin/googlelogin_client';
   if ($config->get('googlelogin.settings')->get('api_type') == 'javascript') {
diff --git a/src/Controller/GoogleLoginController.php b/src/Controller/GoogleLoginController.php
index c9d764a..aaef21e 100644
--- a/src/Controller/GoogleLoginController.php
+++ b/src/Controller/GoogleLoginController.php
@@ -78,6 +78,10 @@ class GoogleLoginController extends ControllerBase {
         if ($tempStore->get('state_destination')) {
           $destination = $tempStore->get('state_destination');
         }
+        if (isset($_COOKIE['google_login_destination'])) {
+          $destination = $_COOKIE['google_login_destination'];
+          setcookie('google_login_destination', '', time() - 3600, '/');
+        }
         $tempStore->delete('state_destination');
         googlelogin_user_exist($payload);
         if ($destination) {
@@ -104,6 +108,11 @@ class GoogleLoginController extends ControllerBase {
         $tempStore->delete('state_destination');
         return new RedirectResponse(Url::fromUserInput($destination)->toString());
       }
+      if (isset($_COOKIE['google_login_destination'])) {
+        $destination = $_COOKIE['google_login_destination'];
+        setcookie('google_login_destination', '', time() - 3600, '/');
+        return new RedirectResponse(Url::fromUserInput($destination)->toString());
+      }
     }
     return $this->redirect('<front>');
   }
