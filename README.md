# Mezabirza.lv

## Local development

### [Setup](https://docs.lando.dev/basics/installation.html)

1. Install the latest [Lando](https://github.com/lando/lando/releases) and read the [documentation](https://docs.lando.dev/).
2. Add to your `/etc/hosts`:
```
127.0.0.1       elasticsearch.lndo.site
127.0.0.1       mezabirza-d10.lndo.site
127.0.0.1       mail.lndo.site
127.0.0.1       kibana.lndo.site
```
3. Start lando: `lando start`
4. Import initial db: `lando db-import ./config/init.sql`
5. Update everything:
```
lando composer install
lando drush cr
lando drush updb -y
lando drush cim -y
lando drush uli
```

6. Build frontend:
```
cd web/themes/custom/mb
lando npm install
lando npm run build
```

**Add bookmarks to your browser to:**
1. https://mezabirza-d10.lndo.site
2. http://mail.lndo.site
3. http://kibana.lndo.site
