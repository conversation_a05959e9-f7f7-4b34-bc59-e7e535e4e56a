<?php

namespace Drupal\userpoints\Plugin\Field\FieldFormatter;

use <PERSON>upal\Core\Field\FieldItemInterface;
use <PERSON><PERSON>al\Core\Field\FieldItemListInterface;
use Drupal\Core\Field\FormatterBase;

/**
 * Plugin implementation of the 'transaction_entity' formatter.
 *
 * @FieldFormatter(
 *   id = "transaction_entity",
 *   label = @Translation("Transaction entity"),
 *   field_types = {
 *     "string"
 *   }
 * )
 */
class TransactionEntity extends FormatterBase {

  /**
   * {@inheritdoc}
   */
  public function viewElements(FieldItemListInterface $items, $langcode) {
    $elements = [];

    foreach ($items as $delta => $item) {
      $elements[$delta] = $this->viewValue($item);
    }

    return $elements;
  }

  /**
   * Generate the output appropriate for one field item.
   *
   * @param \Drupal\Core\Field\FieldItemInterface $item
   *   One field item.
   *
   * @return array
   *   The link output.
   *
   * @throws \Drupal\Component\Plugin\Exception\InvalidPluginDefinitionException
   * @throws \Drupal\Component\Plugin\Exception\PluginNotFoundException
   * @throws \Drupal\Core\Entity\EntityMalformedException
   */
  protected function viewValue(FieldItemInterface $item) {
    if (empty($item->value) || strpos($item->value, ':') === FALSE) {
      return [];
    }
    [$entity_type, $entity_id] = explode(':', $item->value);
    if (!\Drupal::entityTypeManager()->hasHandler($entity_type, 'storage') || !is_numeric($entity_id)) {
      return [];
    }
    $entity = \Drupal::entityTypeManager()->getStorage($entity_type)->load($entity_id);
    if (!$entity) {
      return [];
    }

    $title = $entity->label();
    if ($entity->getEntityTypeId() == 'subscription') {
      $title = ucfirst(str_replace('_', ' ', $entity->name->value));
    }
    if ($entity->getEntityTypeId() == 'commerce_order') {
      $title = ucfirst(str_replace('_', ' ', $entity->getEntityTypeId())) . ' ' . $entity->id();
    }

    return [
      '#type' => 'link',
      '#url' => $entity->toUrl(),
      '#title' => $title,
    ];
  }

}
