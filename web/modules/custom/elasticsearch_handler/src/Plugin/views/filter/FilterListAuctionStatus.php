<?php

namespace Drupal\elasticsearch_handler\Plugin\views\filter;

use Drupal\views\Plugin\views\filter\InOperator;

/**
 * Provides filter that lists auction status.
 *
 * @ViewsFilter("filter_auction_status")
 *
 * @package Drupal\elasticsearch_handler\Plugin\views\filter
 */
class FilterListAuctionStatus extends InOperator {

  /**
   * Content owner values.
   */
  public function getValueOptions() {
    $this->valueOptions = auctions_auction_status_allowed_values_callback();
    return $this->valueOptions;
  }

}
