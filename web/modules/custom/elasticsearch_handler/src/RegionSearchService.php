<?php

namespace Drupal\elasticsearch_handler;

use Dr<PERSON>al\Core\Language\LanguageManagerInterface;
use Drupal\Core\Site\Settings;
use Elastic\Elasticsearch\Client;

/**
 * Provides region finder by geofield using Elasticsearch.
 */
class RegionSearchService {

  /**
   * The language manager.
   *
   * @var \Drupal\Core\Language\LanguageManagerInterface
   */
  protected $languageManager;

  /**
   * The settings.
   *
   * @var \Drupal\Core\Site\Settings
   */
  protected $settings;

  /**
   * The Elasticsearch client.
   *
   * @var \Elastic\Elasticsearch\Client
   */
  protected $client;

  /**
   * Constructs a new RegionSearchService object.
   *
   * @param \Drupal\Core\Language\LanguageManagerInterface $language_manager
   *   The language manager.
   * @param \Drupal\Core\Site\Settings $settings
   *   The settings.
   * @param \Elastic\Elasticsearch\Client $client
   *   The Elasticsearch client.
   */
  public function __construct(LanguageManagerInterface $language_manager, Settings $settings, Client $client) {
    $this->languageManager = $language_manager;
    $this->settings = $settings;
    $this->client = $client;
  }

  /**
   * Searches the regions index for a region that intersects with a geofield.
   *
   * @param string $geofield_value
   *   The geofield value.
   *
   * @return int|null
   *   The term id of the intersecting region, or NULL if no region was found.
   */
  public function searchRegions(string $geofield_value) {
    $geofield = \Drupal::service('geofield.geophp')->load($geofield_value);
    if (!$geofield) {
      return NULL;
    }
    $centroid = $geofield->getCentroid();
    // Construct the Elasticsearch query.
    $language = 'lv';
    $env = $this->settings->get('elasticsearch_helper.environment', 'default');
    $query = [
      'index' => 'regions_index_' . $language . '_' . $env,
      'body' => [
        'query' => [
          'bool' => [
            'must' => [
              'match' => [
                'final' => 'true',
              ],
            ],
            'filter' => [
              'geo_shape' => [
                'location' => [
                  'shape' => [
                    'type' => 'point',
                    'coordinates' => [
                      $centroid->getX(),
                      $centroid->getY(),
                    ],
                  ],
                  'relation' => 'intersects',
                ],
              ],
            ],
          ],
        ],
      ],
    ];

    // Execute the query.
    try {
      $response = $this->client->search($query);
    }
    catch (\Exception $e) {
      // Log the exception.
      \Drupal::logger('elasticsearch_handler')->error($e->getMessage());
      return NULL;
    }
    // Return the term id of the first intersecting region,
    // or NULL if no intersecting region was found.
    return $response['hits']['hits'][0]['_source']['id'] ?? NULL;
  }

}
