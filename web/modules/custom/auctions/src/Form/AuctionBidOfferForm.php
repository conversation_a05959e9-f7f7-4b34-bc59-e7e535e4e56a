<?php

namespace Drupal\auctions\Form;

use <PERSON><PERSON>al\Core\Ajax\AjaxResponse;
use <PERSON><PERSON><PERSON>\Core\Ajax\HtmlCommand;
use <PERSON><PERSON>al\Core\Ajax\InvokeCommand;
use <PERSON><PERSON>al\Core\Form\FormBase;
use <PERSON><PERSON>al\Core\Form\FormBuilderInterface;
use <PERSON><PERSON>al\Core\Form\FormStateInterface;
use Dr<PERSON><PERSON>\auctions\AuctionInterface;
use <PERSON><PERSON><PERSON>\auctions\AuctionsHelper;
use <PERSON><PERSON><PERSON>\node\Entity\Node;
use Drupal\userpoints\TransactionHandler;
use Symfony\Component\DependencyInjection\ContainerInterface;

/**
 * Builds auction bid form.
 *
 * @package Drupal\auctions\Form
 */
class AuctionBidOfferForm extends FormBase {

  /**
   * The transaction handler.
   *
   * @var \Drupal\userpoints\TransactionHandler
   */
  protected $transactionHandler;

  /**
   * The Form builder.
   *
   * @var \Drupal\Core\Form\FormBuilderInterface
   */
  protected $formBuilder;

  /**
   * The auction.
   *
   * @var \Drupal\auctions\AuctionInterface
   */
  protected $auction;

  /**
   * The auctions helper..
   *
   * @var \Drupal\auctions\AuctionsHelper
   */
  protected $auctionsHelper;

  /**
   * Constructs a new AuctionBidForm.
   *
   * @param \Drupal\userpoints\TransactionHandler $transaction_handler
   *   The transaction handler.
   * @param \Drupal\Core\Form\FormBuilderInterface $form_builder
   *   The form builder.
   * @param \Drupal\auctions\AuctionsHelper $auctions_helper
   *   The auctions helper.
   */
  public function __construct(TransactionHandler $transaction_handler, FormBuilderInterface $form_builder, AuctionsHelper $auctions_helper) {
    $this->transactionHandler = $transaction_handler;
    $this->formBuilder = $form_builder;
    $this->auctionsHelper = $auctions_helper;
  }

  /**
   * {@inheritdoc}
   */
  public static function create(ContainerInterface $container) {
    return new static(
      $container->get('userpoints.transaction_handler'),
      $container->get('form_builder'),
      $container->get('auctions.helper')
    );
  }

  /**
   * {@inheritdoc}
   */
  public function getFormId() {
    return 'auctions_auction_bid_offer';
  }

  /**
   * {@inheritdoc}
   */
  public function buildForm(array $form, FormStateInterface $form_state, ?AuctionInterface $auction = NULL) {
    $form['#attached']['library'][] = 'auctions/number-input';
    if (!$this->auction) {
      $this->auction = $auction;
    }
    if ($this->auction->hasBids() || $this->auction->getOwnerId() == $this->currentUser()->id()) {
      $form_state->setTriggeringElement([
        '#ajax' => [
          'callback' => [$this, 'submitForm'],
        ],
      ]);
      return [];
    }
    $form['bid_offer'] = [
      '#type' => 'number',
      '#default_value' => number_format($this->auction->getMinBid() - $this->auction->getStep(), 2, '.', ''),
      '#max' => $this->auction->getMinBid() - $this->auction->getStep(),
      '#step' => $this->auction->getStep(),
      '#required' => TRUE,
      '#attributes' => [
        'class' => ['round-' . $auction->getStep()],
        'autocomplete' => 'off',
      ],
    ];

    $form['actions'] = [
      '#type' => 'actions',
    ];
    $form['actions']['submit_offer'] = [
      '#type' => 'submit',
      '#value' => $this->t('Offer bid'),
      '#attributes' => [
        'class' => [
          'use-ajax',
        ],
        'data-disable-refocus' => 'true',
      ],
      '#ajax' => [
        'callback' => [$this, 'submitFormAjax'],
        'event' => 'click',
      ],
    ];
    $form['#attached']['library'][] = 'core/drupal.dialog.ajax';

    $form['messages-bid-offer'] = [
      '#type' => 'html_tag',
      '#tag' => 'div',
      '#weight' => 100,
      '#attributes' => [
        'class' => [
          'form-errors-bid-offer',
        ],
      ],
    ];

    $form['success-bid-offer'] = [
      '#type' => 'html_tag',
      '#tag' => 'h2',
      '#weight' => 100,
      '#attributes' => [
        'class' => [
          'form-success',
        ],
      ],
    ];

    return $form;
  }

  /**
   * {@inheritdoc}
   */
  public function validateForm(array &$form, FormStateInterface $form_state) {
    $form_state->clearErrors();
  }

  /**
   * Submit form handler.
   *
   * @param array $form
   *   The form.
   * @param \Drupal\Core\Form\FormStateInterface $form_state
   *   The form state.
   *
   * @throws \Drupal\Core\Entity\EntityStorageException
   *
   * @return \Drupal\Core\Ajax\AjaxResponse
   *   The ajax response.
   */
  public function submitFormAjax(array &$form, FormStateInterface $form_state) {
    $form_state->setRebuild(FALSE);
    $response = new AjaxResponse();
    $error = '';
    $bid_amount = $form_state->getValue('bid_offer');

    if (fmod(round($bid_amount - $this->auction->getMinBid(), 2), $this->auction->getStep()) !== 0.0) {
      $error = $this->t('Bid step should be @step @currency', [
        '@step' => $this->auction->getStep(),
        '@currency' => $this->auction->currency->value,
      ]);
    }

    if ($bid_amount < $this->auction->getStep()) {
      $error = $this->t('Minimum bid should be at least @bid', ['@bid' => $this->auction->formatPrice($this->auction->getStep())]);
    }

    if ($bid_amount >= $this->auction->getMinBid()) {
      $error = $this->t('Maximum bid offer amount should be @bid', ['@bid' => $this->auction->formatPrice($this->auction->getMinBid() - $this->auction->getStep())]);
    }

    if ($this->auction->hasBids()) {
      $error = $this->t('Auction has bids.');
    }
    if ($error) {
      $form_state->clearErrors();
      $response->addCommand(new HtmlCommand('.form-errors-bid-offer', $error));
      $response->addCommand(new InvokeCommand('.form-errors-bid-offer', 'addClass', ['error']));
      $response->addCommand(new InvokeCommand('#edit-bid-offer', 'addClass', ['error']));
      return $response;
    }

    $node = Node::create([
      'type' => 'auction_offers',
      'title' => $this->auction->field_property_name->value . ' piedāvājums',
      'field_auction_reference' => ['target_id' => $this->auction->id->value],
      'field_user_reference' => ['target_id' => \Drupal::currentUser()->id()],
      'field_auction_offer' => $form_state->getValue('bid_offer'),
    ]);
    $node->save();

    \Drupal::moduleHandler()->invokeAll('auction_offer_made', [
      $this->auction,
      $node,
    ]);

    // Clean up any form errors.
    $response->addCommand(new HtmlCommand('.auctions-auction-bid-offer', '<h2 class="bid-offer-success">' . $this->t('Bid offer successfully sent') . '</h2> <span>' . $this->t('you will be contacted if the offer is binding') . '</span>'));

    return $response;
  }

  /**
   * {@inheritdoc}
   */
  public function submitForm(array &$form, FormStateInterface $form_state) {
    return $response;
  }

}
