<?php

namespace Drupal\auctions;

use Drupal\Core\Entity\EntityTypeManagerInterface;

/**
 * Provides a service for calculating species composition.
 */
class SpeciesCompositionService {

  /**
   * The entity type manager.
   *
   * @var \Drupal\Core\Entity\EntityTypeManagerInterface
   */
  protected EntityTypeManagerInterface $entityTypeManager;

  /**
   * SpeciesCompositionService constructor.
   *
   * @param \Drupal\Core\Entity\EntityTypeManagerInterface $entityTypeManager
   *   The entity type manager.
   */
  public function __construct(EntityTypeManagerInterface $entityTypeManager) {
    $this->entityTypeManager = $entityTypeManager;
  }

  /**
   * Calculate species composition from array.
   *
   * @param array $speciesList
   *   The species list.
   *
   * @return string
   *   The species composition.
   */
  public function calculateSpeciesCompositionFromArray(array $speciesList) {
    if (empty($speciesList)) {
      return '';
    }
    $totalVolume = array_sum(array_map(function ($species) {
      return $species['field_cubic_meters'][0]['value'];
    }, $speciesList));
    if ($totalVolume === 0) {
      return '';
    }

    $speciesPercentages = [];
    foreach ($speciesList as $species) {
      $speciesKey = explode(' ', $species['field_specie'])[0];
      if ($speciesKey === 'Cits' || $speciesKey === 'NV') {
        continue;
      }
      $speciesPercentages[$speciesKey] = $species['field_cubic_meters'][0]['value'] / $totalVolume * 100;
    }

    arsort($speciesPercentages);
    $reference = $speciesPercentages;

    $speciesPercentages = $this->convertPercentagesToDiscreteSum($speciesPercentages, 10);

    $speciesComposition = '';
    $remainingSpecies = [];
    foreach ($speciesPercentages as $key => $percentage) {
      if ($percentage >= 1) {
        $speciesComposition .= $percentage . $key;
      }
      elseif ($reference[$key] > 1.99) {
        $remainingSpecies[] = $key;
      }
    }

    if (!empty($remainingSpecies)) {
      $speciesComposition .= '+' . implode(',', $remainingSpecies);
    }

    return $speciesComposition;
  }

  /**
   * Calculate species composition.
   *
   * @param \Drupal\auctions\AuctionInterface $auction
   *   The auction entity.
   *
   * @return string
   *   The species composition.
   */
  public function calculateSpeciesComposition(AuctionInterface $auction) {
    if ($auction->get('field_species_list')->isEmpty()) {
      return '';
    }
    $speciesList = $auction->get('field_species_list')->referencedEntities();
    $totalVolume = array_sum(array_map(function ($species) {
      return $species->get('field_cubic_meters')->value;
    }, $speciesList));
    if ($totalVolume === 0) {
      return '';
    }

    $speciesPercentages = [];
    foreach ($speciesList as $species) {
      $speciesKey = explode(' ', $species->get('field_specie')->value)[0];
      if ($speciesKey === 'Cits' || $speciesKey === 'NV') {
        continue;
      }
      $speciesPercentages[$speciesKey] = $species->get('field_cubic_meters')->value / $totalVolume * 100;
    }

    arsort($speciesPercentages);
    $reference = $speciesPercentages;

    $speciesPercentages = $this->convertPercentagesToDiscreteSum($speciesPercentages, 10);

    $speciesComposition = '';
    $remainingSpecies = [];
    foreach ($speciesPercentages as $key => $percentage) {
      if ($percentage >= 1) {
        $speciesComposition .= $percentage . $key;
      }
      elseif ($reference[$key] > 1.99) {
        $remainingSpecies[] = $key;
      }
    }

    if (!empty($remainingSpecies)) {
      $speciesComposition .= '+' . implode(',', $remainingSpecies);
    }

    return $speciesComposition;
  }

  /**
   * Convert percentages to discrete sum.
   *
   * @param array $percentages
   *   The percentages.
   * @param int $desired_sum
   *   The desired sum.
   *
   * @return array
   *   The discrete sum.
   */
  private function convertPercentagesToDiscreteSum(array $percentages, int $desired_sum): array {
    if (empty($percentages)) {
      return [];
    }
    // Calculate initial multipliers.
    $initial_multipliers = [];
    $total_parts = 0;

    foreach ($percentages as $key => $percentage) {
      $initial_multipliers[$key] = round($percentage / 100 * $desired_sum);
      $total_parts += end($initial_multipliers);
    }

    // Adjust the multipliers to ensure they sum up to $desired_sum.
    $difference = $desired_sum - $total_parts;

    // Distribute the difference.
    while ($difference != 0) {
      foreach ($initial_multipliers as &$multiplier) {
        if ($difference > 0) {
          $multiplier++;
          $difference--;
        }
        elseif ($difference < 0 && $multiplier > 0) {
          $multiplier--;
          $difference++;
        }
        if ($difference == 0) {
          break;
        }
      }
    }

    return $initial_multipliers;
  }

}
