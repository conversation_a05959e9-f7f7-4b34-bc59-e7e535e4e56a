<?php

namespace Dr<PERSON>al\mezabirza\Plugin\Block;

use <PERSON>upal\Core\Block\BlockBase;
use <PERSON>upal\Core\Form\FormStateInterface;

/**
 * Provides a social block.
 *
 * @Block(
 *   id = "mezabirza_social",
 *   admin_label = @Translation("Social"),
 *   category = @Translation("Custom")
 * )
 */
class SocialBlock extends BlockBase {

  /**
   * {@inheritdoc}
   */
  public function blockSubmit($form, FormStateInterface $form_state) {
    $this->configuration['text'] = $form_state->getValue('text')['value'];
  }

  /**
   * {@inheritdoc}
   */
  public function build() {
    $build['social_links'] = [
      [
        'url' => 'https://www.facebook.com/mezabirzalv',
        'title' => 'Facebook',
        'icon' => 'facebook',
      ],
    ];

    return $build;
  }

}
