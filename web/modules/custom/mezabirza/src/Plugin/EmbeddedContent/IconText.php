<?php

namespace Drupal\mezabirza\Plugin\EmbeddedContent;

use <PERSON>upal\Core\Form\FormStateInterface;
use <PERSON><PERSON>al\Core\StringTranslation\StringTranslationTrait;
use Drupal\ckeditor5_embedded_content\EmbeddedContentInterface;
use Drupal\ckeditor5_embedded_content\EmbeddedContentPluginBase;
use Drupal\file\Entity\File;

/**
 * Plugin ckeditor Icon with text.
 *
 * @EmbeddedContent(
 *   id = "icon_with_text",
 *   label = @Translation("Icon with text"),
 *   description = @Translation("Renders a icon with text"),
 * )
 */
class IconText extends EmbeddedContentPluginBase implements EmbeddedContentInterface {

  use StringTranslationTrait;

  /**
   * {@inheritdoc}
   */
  public function defaultConfiguration() {
    return [
      'icon' => NULL,
      'text' => NULL,
    ];
  }

  /**
   * {@inheritdoc}
   */
  public function build(): array {
    return [
      '#theme' => 'mezabirza_icon_with_text',
      '#icon' => \Drupal::service('file_url_generator')->generateAbsoluteString(File::load(reset($this->configuration['icon']))->getFileUri()),
      '#text' => check_markup($this->configuration['text']['value'], $this->configuration['text']['format'] ?? 'full'),
    ];
  }

  /**
   * {@inheritdoc}
   */
  public function buildConfigurationForm(array $form, FormStateInterface $form_state) {
    $form['icon'] = [
      '#type' => 'managed_file',
      '#title' => $this->t('Icon'),
      '#upload_location' => 'public://embed_icons/',
      '#default_value' => $this->configuration['icon'],
      '#upload_validators' => [
        'file_validate_extensions' => ['jpeg jpg png gif'],
      ],
      '#required' => TRUE,
    ];
    $form['text'] = [
      '#type' => 'text_format',
      '#title' => $this->t('Body'),
      '#format' => $this->configuration['text']['format'] ?? 'full',
      '#default_value' => $this->configuration['text']['value'] ?? '',
      '#required' => TRUE,
    ];

    return $form;
  }

}
