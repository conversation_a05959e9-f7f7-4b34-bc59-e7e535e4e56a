/**
 * @file
 * Exclusive checkbox behavior for Drupal forms.
 */

(function ($, <PERSON><PERSON><PERSON>, once) {
  'use strict';

  /**
   * Behavior to make checkboxes mutually exclusive within BEF groups.
   */
  Drupal.behaviors.exclusiveCheckboxes = {
    attach: function (context, settings) {
      // Target checkboxes within the specific auction type filter group.
      const checkboxes = once(
        'uniqueCheckboxes',
        '.view-auction-preview .form-checkbox',
        context
      );

      // Iterate over the filtered elements using forEach.
      checkboxes.forEach(function (checkbox) {
        checkbox.addEventListener('change', function (event) {
          if (this.checked) {
            // Uncheck all other checkboxes in the group.
            checkboxes.forEach(function (otherCheckbox) {
              if (otherCheckbox !== this) {
                otherCheckbox.checked = false;
              }
            }, this);
          }
        });
      });
    }
  };

})(j<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, once);
