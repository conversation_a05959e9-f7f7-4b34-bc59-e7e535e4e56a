#
# Copy and rename this file to .env at root of this project.
#

# A common use case is to supply database creds via the environment. Edit settings.php
# like so:
#
# $databases['default']['default'] = [
#   'database' =>  getenv('MYSQL_DATABASE'),
#   'driver' => 'mysql',
#   'host' => getenv('MYSQL_HOSTNAME'),
#   'namespace' => 'Drupal\\Core\\Database\\Driver\\mysql',
#   'password' => getenv('MYSQL_PASSWORD'),
#   'port' => getenv('MYSQL_PORT'),
#   'prefix' => '',
#   'username' => getenv('MYSQL_USER'),
# ];
#
# Uncomment and populate as needed.
# MYSQL_DATABASE=
# MYSQL_HOSTNAME=
# MYSQL_PASSWORD=
# MYSQL_PORT=
# MYSQL_USER=

# Another common use case is to set Drush's --uri via environment.
# DRUSH_OPTIONS_URI=http://example.com