name: mezabirza-D10
recipe: drupal10

config:
  php: '8.3'
  via: nginx
  webroot: web
  xdebug: xdebug
  composer_version: 2-latest
  config:
    php: .lando/php.ini
    database: .lando/my.cnf

tooling:
  grumphp:
    description: Runs grumphp commands
    cmd:
      - appserver: ./vendor/bin/grumphp
  reset:
    description: Update app with latest changes
    cmd:
      - appserver: cd /app && composer install
      - appserver: drush cr
      - appserver: drush deploy -y
      - node: cd /app/web/themes/custom/mb/ && npm install
      - node: cd /app/web/themes/custom/mb/ && npm run build
  upgrade:
    description: Update app
    cmd:
      - appserver: composer install
      - appserver: drush cr
      - appserver: drush updb -y
      - appserver: drush cim -y
      - appserver: composer update
      - appserver: drush cr
      - appserver: drush updb -y
      - appserver: drush cex -y
      - node: cd /app/web/themes/custom/mb/ && npm update
      - node: cd /app/web/themes/custom/mb/ && npm run build
  phpcbf:
    description: phpcbf
    cmd:
      - appserver: /app/vendor/bin/phpcbf --standard=phpcs.xml --extensions=php,module,inc,install,profile,theme
  phpcs:
    description: phpcs
    cmd:
      - appserver: /app/vendor/bin/phpcs --standard=phpcs.xml --extensions=php,module,inc,install,profile,theme
  reindex:
    description: Re-index elasticsearch
    cmd:
      - appserver: /app/.lando/reindex.sh
  npm:
    description: Runs npm commands
    service: node
  xdebug:
    description: Loads Xdebug in the selected mode
    cmd:
      - appserver: /app/.lando/xdebug.sh
    user: root
  auction_end:
    description: Auction end script
    cmd:
      - appserver: /app/scripts/auction_end.sh 2>&1 | tee /app/auctionend.log
  sfp_on:
    cmd:
      - appserver: drush en stage_file_proxy
      - appserver: drush config-set stage_file_proxy.settings origin "https://mezabirza.lv" --yes
      - appserver: drush cr
  sfp_off:
    cmd:
      - appserver: drush pmu stage_file_proxy
  blackfire:
    service: appserver
  blackfire-player:
    service: appserver
  codecept:
    cmd:
      - appserver: /app/vendor/bin/codecept

services:
  appserver:
    overrides:
      environment:
        HASH_SALT: notsosecurehash
        DRUSH_OPTIONS_URI: https://mezabirza-d10.lndo.site
        # Support debugging with XDEBUG 3.
        XDEBUG_MODE:
        # Support debugging Drush with XDEBUG 3.
        PHP_IDE_CONFIG: "serverName=appserver"
  mailhog:
    type: mailhog
    scanner: false
    ssl: true
    sslExpose: true
    hogfrom:
      - appserver
  database:
    type: "mysql:8.0.39"
    portforward: 12375
  node:
    type: node:18
  elasticsearch:
    type: compose
    services:
      image: "docker.elastic.co/elasticsearch/elasticsearch:8.10.4"
      command: "/bin/tini -- /usr/local/bin/docker-entrypoint.sh eswrapper"
      user: elasticsearch
      environment:
        ES_JAVA_OPTS: "-Xms512m -Xmx512m"
        discovery.type: "single-node"
        bootstrap.memory_lock: "true"
        # Allow CORS requests.
        http.cors.enabled: "true"
        http.cors.allow-origin: "'*'"
        # Disable authentication in local development
        xpack.security.enabled: "false"
        xpack.security.enrollment.enabled: "false"
      ulimits:
        memlock:
          soft: "-1"
          hard: "-1"
      ports:
        - "9200:9200"
      volumes:
        - esdata:/usr/share/elasticsearch/data
    volumes:
      esdata:
        driver: local
  kibana:
    type: compose
    services:
      image: 'docker.elastic.co/kibana/kibana:8.10.4'
      command: "/bin/tini -- /usr/local/bin/kibana-docker"
      user: kibana
      ports:
        - "5601:5601"
  blackfire:
    type: blackfire
    server_id: 65889f0a-df89-446c-ac8e-e35136b7ac75
    server_token: 8912aeaf95d41035c21911e9a1114ffc1628fa3a9083a44ad16b37739500ee8d
    client_id: 9186ef52-d85e-448f-b1f9-75bb63539dbf
    client_token: 26324ceccbbfebc469669e10923d6b494612d848ffc8b2df7a0f20cbfe886e68

proxy:
  mailhog:
    - mail.lndo.site
  elasticsearch:
    - elasticsearch.lndo.site:9200
  kibana:
    - kibana.lndo.site:5601
  appserver_nginx:
    - mezabirza-d10.lndo.site
    - zemesbirza-d10.lndo.site

env_file:
  - .lando/.env
  - .lando/.env.local

# Tested with Lando version
version: v3.20.4
