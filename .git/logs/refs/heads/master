d07158c54c32aa63417730d1f4a8485354400b1a 7c4386f16d857d3beeb0d4247bd2d0089acb738f Gun<PERSON> Jakovins <<EMAIL>> 1740212248 +0200	commit: Meta repeat 25 auction create commision.
7c4386f16d857d3beeb0d4247bd2d0089acb738f 561804e7a42fcf081bf986c6ef057d7a47d9573c <PERSON>tis Jakovins <<EMAIL>> 1740217150 +0200	commit: Custom newsletters per type and added translations
561804e7a42fcf081bf986c6ef057d7a47d9573c 53867ebb8ac010694aa496ebf1055192702dc36a Guntis Jakovins <<EMAIL>> 1740218191 +0200	commit: Fix invalid newleter config keys.
53867ebb8ac010694aa496ebf1055192702dc36a 2a55c14016f6569b5a8b1c494597f065e9604cd9 Guntis Jakovins <<EMAIL>> 1740235949 +0200	commit: Admin toolbar fix update
2a55c14016f6569b5a8b1c494597f065e9604cd9 3d9c4c33560729b60c322f60271b10d00e4a2e1a Guntis Jakovins <<EMAIL>> 1740306176 +0200	commit: Calculate auction form values from species list and only if it is there.
3d9c4c33560729b60c322f60271b10d00e4a2e1a e5346d11f630a4444c38f00230fd6e44df057f9c Guntis Jakovins <<EMAIL>> 1740307573 +0200	commit: Hide CA file for owner
e5346d11f630a4444c38f00230fd6e44df057f9c 4b088d1352362e7d2ecc14b7d3667a14fc76c300 Guntis Jakovins <<EMAIL>> 1740307641 +0200	commit: quick_node_clone fix update
4b088d1352362e7d2ecc14b7d3667a14fc76c300 5a7ac746e38057e5aa436c07cd7e248998e1923c Guntis Jakovins <<EMAIL>> 1740335165 +0200	commit: Calculator page style fix
5a7ac746e38057e5aa436c07cd7e248998e1923c 4b0b97d215601a22768965b7e833914df5f10139 Guntis Jakovins <<EMAIL>> 1740467373 +0200	commit: Post read event
4b0b97d215601a22768965b7e833914df5f10139 0090c4507eab8cf6305bddb1d6214f138bb61111 Guntis Jakovins <<EMAIL>> 1740475546 +0200	commit: View datalayer and new auction email typo fix.
0090c4507eab8cf6305bddb1d6214f138bb61111 fd402ff407b9ffae8a9260d6401e9c085ff43f91 Guntis Jakovins <<EMAIL>> 1740477236 +0200	commit: Allow to view canceled auction.
fd402ff407b9ffae8a9260d6401e9c085ff43f91 5332bf150f881d498990093ca88f646a039a8256 Guntis Jakovins <<EMAIL>> 1740483385 +0200	commit: Fix auction display
5332bf150f881d498990093ca88f646a039a8256 50071a1bc4a31aff7e5cbc547a88b8de04c8b898 Guntis Jakovins <<EMAIL>> 1740483397 +0200	commit: Fix auction display
50071a1bc4a31aff7e5cbc547a88b8de04c8b898 bb171e5ff28cc4d592450092cdb464e364052a6f Guntis Jakovins <<EMAIL>> 1740821274 +0200	commit: Add to calendar fuctionality from scratch.
bb171e5ff28cc4d592450092cdb464e364052a6f 41cd2cd3309c8e3760ebf1f70b483f52db18e24d Guntis Jakovins <<EMAIL>> 1740821716 +0200	commit: Updated translation file
41cd2cd3309c8e3760ebf1f70b483f52db18e24d 2f56afc570c29e8932bb2292464d89b7e0a09f69 Guntis Jakovins <<EMAIL>> 1740821958 +0200	commit: Leaflet, elasticsearch and actions update.
2f56afc570c29e8932bb2292464d89b7e0a09f69 1d6e7e9a07905281d8db042cf80287446e75aa95 Guntis Jakovins <<EMAIL>> 1740824191 +0200	commit: Patch leaflet module to update markers.
1d6e7e9a07905281d8db042cf80287446e75aa95 aa532c078d8b5c6af23040296eebdf3b013d46d4 Guntis Jakovins <<EMAIL>> 1740894959 +0200	commit: Felling volume description update and alteration for display.
aa532c078d8b5c6af23040296eebdf3b013d46d4 585a5823b7600a796204779002c71ba0003919fb Guntis Jakovins <<EMAIL>> 1740895714 +0200	commit: Canceled auction end time and display fix.
585a5823b7600a796204779002c71ba0003919fb 02ce2aa3ffa97ada044683ab228fcbcba8c92cd1 Guntis Jakovins <<EMAIL>> 1740899902 +0200	commit: Indicate that auction must be submitted.
02ce2aa3ffa97ada044683ab228fcbcba8c92cd1 d936d951387305b42105b8c20a5c36e2e120b527 Guntis Jakovins <<EMAIL>> 1741067140 +0200	merge feat/new_components: Fast-forward
d936d951387305b42105b8c20a5c36e2e120b527 53489d468ea7f38357065a12c86a73d1ef5be6a8 Guntis Jakovins <<EMAIL>> 1741067403 +0200	commit: Fix end time in newsletter
53489d468ea7f38357065a12c86a73d1ef5be6a8 218aa71234a0d796898a3b45a19e40073f357c28 Guntis Jakovins <<EMAIL>> 1741068164 +0200	commit: Fix end time in newsletter
218aa71234a0d796898a3b45a19e40073f357c28 54d2276c261866dae38d167a0060ee6e3a313d84 Guntis Jakovins <<EMAIL>> 1741110570 +0200	commit: Video banner in grid
54d2276c261866dae38d167a0060ee6e3a313d84 c169b3a42cc25e4bdfca43f1f649763e8e1962d8 Guntis Jakovins <<EMAIL>> 1741153257 +0200	commit: Calculator style fix
c169b3a42cc25e4bdfca43f1f649763e8e1962d8 4da0edcaef25ecbbfea63d4f263a3ddf51afc0e1 Guntis Jakovins <<EMAIL>> 1741156096 +0200	commit: KPI user statistics
4da0edcaef25ecbbfea63d4f263a3ddf51afc0e1 56d786e331c2600a2c7acd4a4f2e448d42d4e856 Guntis Jakovins <<EMAIL>> 1741157154 +0200	commit: All all auctions view
56d786e331c2600a2c7acd4a4f2e448d42d4e856 bb86acc14b9e08988bab5386d6997114080544c2 Guntis Jakovins <<EMAIL>> 1741159946 +0200	commit: Update event routes
bb86acc14b9e08988bab5386d6997114080544c2 5bb6935d35bcddd9734625cfaa6cf9f4102055eb Guntis Jakovins <<EMAIL>> 1741415314 +0200	commit: Fonts, buttons, calculatotor fix, hide label option
5bb6935d35bcddd9734625cfaa6cf9f4102055eb f78869652e6e8b329f338e84a7c23c384d9980ff Guntis Jakovins <<EMAIL>> 1741416191 +0200	commit: New bidders aggregation fix
f78869652e6e8b329f338e84a7c23c384d9980ff 503b9fbdcc40ef806c7242a90d69b0de70ebb3f3 Guntis Jakovins <<EMAIL>> 1741416430 +0200	commit: Flex Auction operations.
503b9fbdcc40ef806c7242a90d69b0de70ebb3f3 f5b04e66deb1e44b4c713b60de4a2d785e1b7db2 Guntis Jakovins <<EMAIL>> 1741419384 +0200	commit: Core,leaflet, charts update and fix. Price calculation fix.
f5b04e66deb1e44b4c713b60de4a2d785e1b7db2 b2879c2130153b3142cc83dac1883fae76c6b505 Guntis Jakovins <<EMAIL>> 1741419504 +0200	commit: Fix debug
b2879c2130153b3142cc83dac1883fae76c6b505 5ef89a1a4c5f81d48f884bf81ba5d825d338882e Guntis Jakovins <<EMAIL>> 1741419956 +0200	commit: Fix full calc glitch
5ef89a1a4c5f81d48f884bf81ba5d825d338882e ec20467903be998e2459798e3a8c8dcab9d540dc Guntis Jakovins <<EMAIL>> 1741433483 +0200	commit: Refactor frontpage auction view to use elasticsearch add filtering tabs with only visible options.
ec20467903be998e2459798e3a8c8dcab9d540dc 4722531986413d392f3477b7b187d7a0d973e1ab Guntis Jakovins <<EMAIL>> 1741436122 +0200	revert: Revert "All all auctions view"
4722531986413d392f3477b7b187d7a0d973e1ab 26b15a45f46dbc52d4a3f02eecdf7c08b0faa4b1 Guntis Jakovins <<EMAIL>> 1741436159 +0200	commit: fix all auctions view. Add datalayer fix
ec20467903be998e2459798e3a8c8dcab9d540dc 26b15a45f46dbc52d4a3f02eecdf7c08b0faa4b1 Guntis Jakovins <<EMAIL>> 1741438702 +0200	pull: Fast-forward
26b15a45f46dbc52d4a3f02eecdf7c08b0faa4b1 7f08d37986a7c3baa1b349217951903b4e97620e Guntis Jakovins <<EMAIL>> 1741440017 +0200	commit: Tmp access fix
7f08d37986a7c3baa1b349217951903b4e97620e 52c0a7d6fdec2cd5032a9ad4930370706f1ec392 Guntis Jakovins <<EMAIL>> 1741462156 +0200	commit: Propper access handling
52c0a7d6fdec2cd5032a9ad4930370706f1ec392 5b0aae04c5e3d6c269812ce9b862e583215e6c25 Guntis Jakovins <<EMAIL>> 1741463956 +0200	commit: Fix all auction view
5b0aae04c5e3d6c269812ce9b862e583215e6c25 48bd63038e5db22519a3a93bd667be774f6ab239 Guntis Jakovins <<EMAIL>> 1741502210 +0200	commit: Jide front filters. Install gin 4.0
48bd63038e5db22519a3a93bd667be774f6ab239 b231ac608b7e1dca3c390bb7e3826b0ed7201cfd Guntis Jakovins <<EMAIL>> 1741504192 +0200	commit: Fix coocie complieance issues.
b231ac608b7e1dca3c390bb7e3826b0ed7201cfd 4970fc0a5faf21108a8bed7b3b8d354a2070096d Guntis Jakovins <<EMAIL>> 1741507021 +0200	commit: heading fix, seo for login/signup, contact form accessibility
4970fc0a5faf21108a8bed7b3b8d354a2070096d 4f2b5b89c4a0a2f54c84208a60ccd358c3e5d68d Guntis Jakovins <<EMAIL>> 1741508059 +0200	commit: Fix h3
4f2b5b89c4a0a2f54c84208a60ccd358c3e5d68d d2a970d386afebd41b1185db8847690d8070b060 Guntis Jakovins <<EMAIL>> 1741510687 +0200	commit: Gin upgrade with toolbar and enable toolbar
d2a970d386afebd41b1185db8847690d8070b060 17be7a8177fa3867cf0b7e2cb8426b284b27e06a Guntis Jakovins <<EMAIL>> 1741511280 +0200	commit: Fix access controll only for auctions leave rest alone
17be7a8177fa3867cf0b7e2cb8426b284b27e06a d087993c9035d109a53367b4ebdd7d843c22e901 Guntis Jakovins <<EMAIL>> 1741627727 +0200	commit: Image paragraph fixes.
d087993c9035d109a53367b4ebdd7d843c22e901 df5bab66adc329e0110f3af0d1a40a428101041e Guntis Jakovins <<EMAIL>> 1741629654 +0200	cherry-pick: Fix gaps and video margins
df5bab66adc329e0110f3af0d1a40a428101041e ef71319c7a0a72ce272b0fa8074d89a9eede83d3 Guntis Jakovins <<EMAIL>> 1741629931 +0200	commit: Fix gaps and video margins
ef71319c7a0a72ce272b0fa8074d89a9eede83d3 bb404e2ec4b55eed3e902bad7c3c6fc3f9480766 Guntis Jakovins <<EMAIL>> 1741631414 +0200	commit: Auction tokens and banner fixes
bb404e2ec4b55eed3e902bad7c3c6fc3f9480766 e836a1fdca067858fbcf3c76a132e0ad69511e60 Guntis Jakovins <<EMAIL>> 1741631588 +0200	commit: VBO update
e836a1fdca067858fbcf3c76a132e0ad69511e60 e2a531cdda95bd23a10581692751400b738d7e67 Guntis Jakovins <<EMAIL>> 1741632871 +0200	commit: Grid adjustments
e2a531cdda95bd23a10581692751400b738d7e67 b93dc8adc6f1aa9135dab1ac3d6fbc295fa12b95 Guntis Jakovins <<EMAIL>> 1741633489 +0200	commit: Small tesaer style fix
b93dc8adc6f1aa9135dab1ac3d6fbc295fa12b95 4d879c8b7eae12f9a42d2cdd44c6932b6666876c Guntis Jakovins <<EMAIL>> 1743154560 +0200	commit: Updates
4d879c8b7eae12f9a42d2cdd44c6932b6666876c 41b7f9d9b2070b17e46ec194941af1fdc6a1bbf9 Guntis Jakovins <<EMAIL>> 1743580688 +0300	commit: DDev and disable gin toolbar and package updates
41b7f9d9b2070b17e46ec194941af1fdc6a1bbf9 f9458b8f1ad3203129fc05a5734e9d35a8af1e95 Guntis Jakovins <<EMAIL>> 1743580698 +0300	commit: DDev and disable gin toolbar and package updates
f9458b8f1ad3203129fc05a5734e9d35a8af1e95 97560a1f5a25a8688340825dcb4d4ad0070e9b8a Guntis Jakovins <<EMAIL>> 1744441205 +0300	commit: Core and charts update
97560a1f5a25a8688340825dcb4d4ad0070e9b8a db9af448da4f896c540e38a4e6f11f9db60d25f7 Guntis Jakovins <<EMAIL>> 1744441809 +0300	commit: Optimize google search description keywords
db9af448da4f896c540e38a4e6f11f9db60d25f7 951b7af29369ce0bdbb80c66ef49de911d6b636e Guntis Jakovins <<EMAIL>> 1744443587 +0300	commit: Consent mode
951b7af29369ce0bdbb80c66ef49de911d6b636e 0c6fafefeab88ef40049029ce169daa7f2485ddd Guntis Jakovins <<EMAIL>> 1744445829 +0300	commit: Consent too late fix
0c6fafefeab88ef40049029ce169daa7f2485ddd df1e778a2cd65fbbd70d3a0751478d689ab8f7a9 Guntis Jakovins <<EMAIL>> 1744446396 +0300	commit: Consent too late fix
df1e778a2cd65fbbd70d3a0751478d689ab8f7a9 0c5fb24111652f9e3903aa59935c5af531779909 Guntis Jakovins <<EMAIL>> 1744523676 +0300	commit: Update pusher version. Fix console logs. Update packages.
0c5fb24111652f9e3903aa59935c5af531779909 14e30fc41f3adde6abcae99306a865a8fb4cc08d Guntis Jakovins <<EMAIL>> 1744525021 +0300	commit: Offer email updates and translations
14e30fc41f3adde6abcae99306a865a8fb4cc08d 297b5e8897d96dbf94fe7c4d8843e826c4ab6eec Guntis Jakovins <<EMAIL>> 1744569926 +0300	commit: Fix log errors
297b5e8897d96dbf94fe7c4d8843e826c4ab6eec de305c3a21f8bd6fa333166eb64f6ad455ace73b Guntis Jakovins <<EMAIL>> 1744804932 +0300	commit: Disable validation for field_total_forest_stock for editors
de305c3a21f8bd6fa333166eb64f6ad455ace73b 44696d2547c91671909625a39ab0ad0cbe2e6259 Guntis Jakovins <<EMAIL>> 1744951488 +0300	commit: Module updates install domain module.
44696d2547c91671909625a39ab0ad0cbe2e6259 9d494524811094cda387bd4fe95c320be924fac1 Guntis Jakovins <<EMAIL>> 1745041549 +0300	commit: Domain settings
9d494524811094cda387bd4fe95c320be924fac1 1ec8fae998e3b677d0ad8060ec3ac2c55d5aee25 Guntis Jakovins <<EMAIL>> 1745046361 +0300	commit: Rename Cones to Points
1ec8fae998e3b677d0ad8060ec3ac2c55d5aee25 4773c09e86fe37b1b190c19f4ab5c919a90eb373 Guntis Jakovins <<EMAIL>> 1745072353 +0300	commit: Filter option fixes. Domain, view schema fixes.
4773c09e86fe37b1b190c19f4ab5c919a90eb373 2a1bea5f3b517374bf379315e6832ab3ef0308e2 Guntis Jakovins <<EMAIL>> 1745211413 +0300	commit: Logo update
2a1bea5f3b517374bf379315e6832ab3ef0308e2 64fdc6a8bc64ad1bd6ab456b595035457b900336 Guntis Jakovins <<EMAIL>> 1745214216 +0300	commit: Link colors
64fdc6a8bc64ad1bd6ab456b595035457b900336 af9a2212e481b0790a02073712646f4de74ab0a1 Guntis Jakovins <<EMAIL>> 1745215061 +0300	commit: Set domain required_cache_contexts
af9a2212e481b0790a02073712646f4de74ab0a1 051a95591aa58e81bf9a91c30564168f5ebcd5ec Guntis Jakovins <<EMAIL>> 1745643330 +0300	commit: New relic js agent install
051a95591aa58e81bf9a91c30564168f5ebcd5ec 6dbd36f9039795d527a66d63cb4f34294975a723 Guntis Jakovins <<EMAIL>> 1745644189 +0300	commit: End timer and filtering fix.
6dbd36f9039795d527a66d63cb4f34294975a723 e9943c9aa9cc0b81651174c4c289002488129ce9 Guntis Jakovins <<EMAIL>> 1745645076 +0300	commit: Tansaction view fixes.
e9943c9aa9cc0b81651174c4c289002488129ce9 46cabff7764a88140900acd1745b359149cedd63 Guntis Jakovins <<EMAIL>> 1745645493 +0300	commit: Hover color fix.
46cabff7764a88140900acd1745b359149cedd63 505670b1ea24d59e9461e709925714e8fc404cd3 Guntis Jakovins <<EMAIL>> 1745646306 +0300	commit: Do not subscribe to newsletters on zemesbirza. Recaptcha domain registration.
505670b1ea24d59e9461e709925714e8fc404cd3 ac0ed31d5364d22be6d07a6b17329b4092ebf2aa Guntis Jakovins <<EMAIL>> 1745648563 +0300	commit: Land lease list paragraph. Fix auction view. Adds translations for mising fields.
ac0ed31d5364d22be6d07a6b17329b4092ebf2aa 7a1bf719810ea76fe7605829dbeb4ec25c6bcd50 Guntis Jakovins <<EMAIL>> 1745656690 +0300	commit: Fix mobile logo image
7a1bf719810ea76fe7605829dbeb4ec25c6bcd50 289ea118f2149f6f3ecf583bf6b1927ff6694f3b Guntis Jakovins <<EMAIL>> 1745657507 +0300	commit: Module updates and leaflet patch.
289ea118f2149f6f3ecf583bf6b1927ff6694f3b c2c0165f5db4a80aa7be0d48418547900d90660a Guntis Jakovins <<EMAIL>> 1745657611 +0300	commit: Rename menus
c2c0165f5db4a80aa7be0d48418547900d90660a 607600544726f74c9d695a60e604197e11642f38 Guntis Jakovins <<EMAIL>> 1745658154 +0300	commit: Filtering show only active auctions
607600544726f74c9d695a60e604197e11642f38 b5eb2375df11b4961f6db2ee82cc3dcbc4ebb6cb Guntis Jakovins <<EMAIL>> 1746685620 +0300	commit: Translation and module updates, fix invoice changes.
b5eb2375df11b4961f6db2ee82cc3dcbc4ebb6cb c3b27a54ea6fcb4ffe4ea88260d6a6740fab1300 Guntis Jakovins <<EMAIL>> 1746854581 +0300	commit: Welcome page settings per domain, email domain, translations
c3b27a54ea6fcb4ffe4ea88260d6a6740fab1300 31316290bbe788511bb60fc4408d9b00d6c5ac23 Guntis Jakovins <<EMAIL>> 1746856564 +0300	commit: Entity print update and patch refactor + test
31316290bbe788511bb60fc4408d9b00d6c5ac23 4d2bfe360724f3cb4ec53ff690a4237940ef37f9 Guntis Jakovins <<EMAIL>> 1746857083 +0300	commit: Update hook fix.
4d2bfe360724f3cb4ec53ff690a4237940ef37f9 b4210495257d3d02754328b1a18bbac849f8f208 Guntis Jakovins <<EMAIL>> 1746859097 +0300	commit: Logo update
b4210495257d3d02754328b1a18bbac849f8f208 8920ad06ccdd57b1dd8ae78b7be3e908ac776037 Guntis Jakovins <<EMAIL>> 1746863035 +0300	commit: Email style fixes
8920ad06ccdd57b1dd8ae78b7be3e908ac776037 a5bee302a437767d5c9fef04ec90748a827c63b8 Guntis Jakovins <<EMAIL>> 1746942468 +0300	commit: Registration, menu and footer domain configs.
a5bee302a437767d5c9fef04ec90748a827c63b8 7f07a1721c330f772bf9cbf64fd30a43b5a0b361 Guntis Jakovins <<EMAIL>> 1746943449 +0300	commit: Validate user can participate in custom auction. Add translations.
7f07a1721c330f772bf9cbf64fd30a43b5a0b361 508adbd32bf72c6be176c7a8f2cfe7c55f2c1b3b Guntis Jakovins <<EMAIL>> 1746944537 +0300	commit: Domain module patch
508adbd32bf72c6be176c7a8f2cfe7c55f2c1b3b 18bf96b282303b15cfb353e09e51fb572752e39e Guntis Jakovins <<EMAIL>> 1746946251 +0300	commit: Front view fixes
18bf96b282303b15cfb353e09e51fb572752e39e 1ffe5cfbbbcfd294d6e5c6fdaaf1aa1df36242e9 Guntis Jakovins <<EMAIL>> 1747290935 +0300	commit: Registration fee and translation fixes.
1ffe5cfbbbcfd294d6e5c6fdaaf1aa1df36242e9 7c84c9c891d259b608c98e36aab378d8fa7da1f9 Guntis Jakovins <<EMAIL>> 1747291476 +0300	commit: Registration fee and translation fixes.
7c84c9c891d259b608c98e36aab378d8fa7da1f9 8a278055efff0d1211b9747168042c02b036a587 Guntis Jakovins <<EMAIL>> 1747292144 +0300	commit: Step sizes and price per display.
8a278055efff0d1211b9747168042c02b036a587 7a3d59538574e27c0f0e968d5330980bc209295d Guntis Jakovins <<EMAIL>> 1747388745 +0300	commit: Auction view updates
7a3d59538574e27c0f0e968d5330980bc209295d 9357bbcc0690eeff72f00fe9bf35165bd17f3465 Guntis Jakovins <<EMAIL>> 1747392249 +0300	commit: Update styles
9357bbcc0690eeff72f00fe9bf35165bd17f3465 bfdecf52296cde253c276666c83f6407fc8462df Guntis Jakovins <<EMAIL>> 1747393204 +0300	commit: Update styles
bfdecf52296cde253c276666c83f6407fc8462df 2b0510e5ef9a094689dfdd9ec99c7fd973d5daaf Guntis Jakovins <<EMAIL>> 1747393735 +0300	commit: Field updates and welcome page updates
2b0510e5ef9a094689dfdd9ec99c7fd973d5daaf ecf70a67467afed4cf8af40212aa89976176aae5 Guntis Jakovins <<EMAIL>> 1747458311 +0300	commit: Summ currency fix
ecf70a67467afed4cf8af40212aa89976176aae5 b2377352f0d9bd5aae7f28308a8f53cf3bcd93f1 Guntis Jakovins <<EMAIL>> 1747458696 +0300	commit: Autobid fix
b2377352f0d9bd5aae7f28308a8f53cf3bcd93f1 406c314d9eb235950ae366955625d9c29c4c590c Guntis Jakovins <<EMAIL>> 1747459743 +0300	commit: Offer form fixes
406c314d9eb235950ae366955625d9c29c4c590c 02b57a058415207f4f08044c332e925ddbd2c0d1 Guntis Jakovins <<EMAIL>> 1747460339 +0300	commit: Remove fee info from confirm bid if free auction
02b57a058415207f4f08044c332e925ddbd2c0d1 0c9740c7736517a989732d006ac1b4c0a0723321 Guntis Jakovins <<EMAIL>> 1747461191 +0300	commit: Liz field fix in auction form
0c9740c7736517a989732d006ac1b4c0a0723321 221028e720424fe011a1905beb37783aaafee8d6 Guntis Jakovins <<EMAIL>> 1747895430 +0300	pull: Fast-forward
221028e720424fe011a1905beb37783aaafee8d6 a62478755b9c98be0ca773df8420eb6bda863b37 Guntis Jakovins <<EMAIL>> 1747905977 +0300	commit: Land lease fixes
a62478755b9c98be0ca773df8420eb6bda863b37 65158dc75e1f81c14a5b0d478c535fd8504c4d84 Guntis Jakovins <<EMAIL>> 1747972758 +0300	commit: Front page view updates
65158dc75e1f81c14a5b0d478c535fd8504c4d84 ca04aa3ac8ec67029e5eb1a7f24f004c3e9ae1a4 Guntis Jakovins <<EMAIL>> 1747982997 +0300	commit: Front page view updates
ca04aa3ac8ec67029e5eb1a7f24f004c3e9ae1a4 e036855bfc1780913d92057542df1cf98089cbe0 Guntis Jakovins <<EMAIL>> 1747984284 +0300	pull: Fast-forward
e036855bfc1780913d92057542df1cf98089cbe0 041c89283f165ce11fa5af10332bf781fc7407ad Guntis Jakovins <<EMAIL>> 1747986751 +0300	commit: Front page view updates
041c89283f165ce11fa5af10332bf781fc7407ad b09adc3d2748be3c6179cde7c1ba9abec1ceb7b5 Guntis Jakovins <<EMAIL>> 1748146867 +0300	commit: Translations and admin toolbar and leaflet update
